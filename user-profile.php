<?php
require_once 'includes/auth-init.php';

$userId = intval($_GET['id'] ?? 0);
if (!$userId) {
    header('Location: community.php');
    exit;
}

try {
    $db = db();
    
    // 获取用户信息
    $sql = "SELECT u.*, up.nickname, up.bio, up.signature, up.avatar_url, up.website, 
                   up.location, up.post_count, up.comment_count, up.like_received_count,
                   up.created_at as profile_created_at
            FROM users u
            LEFT JOIN user_profiles up ON u.id = up.user_id
            WHERE u.id = ?";
    
    $user = $db->fetchOne($sql, [$userId]);
    
    if (!$user) {
        header('Location: community.php');
        exit;
    }
    
    // 获取用户的帖子列表
    $postsPerPage = 10;
    $page = intval($_GET['page'] ?? 1);
    $offset = ($page - 1) * $postsPerPage;
    
    $postsSql = "SELECT p.*, pc.name as category_name, pc.color as category_color
                 FROM posts p
                 LEFT JOIN post_categories pc ON p.category_id = pc.id
                 WHERE p.user_id = ? AND p.status = 'published'
                 ORDER BY p.created_at DESC
                 LIMIT ? OFFSET ?";
    
    $posts = $db->fetchAll($postsSql, [$userId, $postsPerPage, $offset]);
    
    // 获取总帖子数
    $totalPosts = $db->fetchOne("SELECT COUNT(*) as count FROM posts WHERE user_id = ? AND status = 'published'", [$userId])['count'];
    $totalPages = ceil($totalPosts / $postsPerPage);
    
    // 获取用户的最新评论
    $recentCommentsSql = "SELECT c.*, p.title as post_title, p.id as post_id
                          FROM comments c
                          LEFT JOIN posts p ON c.post_id = p.id
                          WHERE c.user_id = ? AND c.status = 'published'
                          ORDER BY c.created_at DESC
                          LIMIT 5";
    
    $recentComments = $db->fetchAll($recentCommentsSql, [$userId]);
    
    // 检查是否关注了该用户
    $isFollowing = false;
    if ($currentUser && $currentUser['id'] != $userId) {
        $followCheck = $db->fetchOne(
            "SELECT id FROM follows WHERE follower_id = ? AND following_id = ?",
            [$currentUser['id'], $userId]
        );
        $isFollowing = (bool)$followCheck;
    }
    
    // 获取关注统计
    $followStats = $db->fetchOne(
        "SELECT 
            (SELECT COUNT(*) FROM follows WHERE following_id = ?) as followers_count,
            (SELECT COUNT(*) FROM follows WHERE follower_id = ?) as following_count",
        [$userId, $userId]
    );
    
} catch (Exception $e) {
    error_log("用户资料页错误: " . $e->getMessage());
    header('Location: community.php');
    exit;
}

// 辅助函数
function timeAgo($datetime) {
    if (empty($datetime)) {
        return '未知时间';
    }

    // 尝试多种时间格式解析
    $timestamp = false;

    // 首先尝试直接解析
    $timestamp = strtotime($datetime);

    // 如果失败，尝试其他格式
    if ($timestamp === false) {
        // 尝试ISO格式
        $timestamp = strtotime(str_replace('T', ' ', $datetime));
    }

    // 如果还是失败，尝试手动解析
    if ($timestamp === false) {
        // 匹配 YYYY-MM-DD HH:MM:SS 格式
        if (preg_match('/(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/', $datetime, $matches)) {
            $timestamp = mktime($matches[4], $matches[5], $matches[6], $matches[2], $matches[3], $matches[1]);
        }
    }

    if ($timestamp === false) {
        return '时间格式错误';
    }

    $currentTime = time();
    $time = $currentTime - $timestamp;

    // 如果时间差为负数（未来时间），可能是时区问题
    if ($time < 0) {
        $time = abs($time);
    }

    // 3分钟内显示"刚刚"
    if ($time < 180) return '刚刚';

    // 1小时内显示分钟
    if ($time < 3600) return floor($time/60) . '分钟前';

    // 24小时内显示小时
    if ($time < 86400) return floor($time/3600) . '小时前';

    // 30天内显示天数
    if ($time < 2592000) return floor($time/86400) . '天前';

    // 12个月内显示月数
    if ($time < 31536000) return floor($time/2592000) . '个月前';

    // 超过12个月显示年数
    return floor($time/31536000) . '年前';
}

function formatNumber($num) {
    if ($num >= 1000000) return round($num/1000000, 1) . 'M';
    if ($num >= 1000) return round($num/1000, 1) . 'K';
    return $num;
}

function escapeHtml($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escapeHtml($user['nickname'] ?? $user['full_name'] ?? $user['username']); ?> - 用户资料</title>
    <link rel="stylesheet" href="assets/css/community.css">
    <link rel="stylesheet" href="assets/css/user-profile.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.php">
                    <img src="assets/images/logo.png" alt="比特熊智慧系统" class="logo">
                    <span>比特熊智慧系统</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <a href="index.php" class="nav-link">首页</a>
                <a href="community.php" class="nav-link">社区</a>
                <a href="#" class="nav-link">课程</a>
                <a href="#" class="nav-link">文档</a>
            </div>
            
            <div class="nav-actions">
                <?php if ($currentUser): ?>
                    <a href="community-post.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 发帖
                    </a>
                    <a href="bookmarks.php" class="nav-link" title="我的收藏">
                        <i class="fas fa-bookmark"></i>
                    </a>
                    <div class="user-menu">
                        <img src="<?php echo $currentUser['avatar'] ?? 'assets/images/default-avatar.png'; ?>"
                             alt="头像" class="user-avatar">
                        <span><?php echo escapeHtml($currentUser['username']); ?></span>
                    </div>
                <?php else: ?>
                    <a href="admin-login.php" class="btn btn-outline">登录</a>
                    <a href="register.php" class="btn btn-primary">注册</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 返回按钮 -->
            <div class="breadcrumb">
                <a href="community.php" class="breadcrumb-link">
                    <i class="fas fa-arrow-left"></i> 返回社区
                </a>
            </div>

            <!-- 用户资料卡片 -->
            <div class="profile-card">
                <div class="profile-header">
                    <div class="profile-avatar">
                        <img src="<?php echo $user['avatar_url'] ?? 'assets/images/default-avatar.png'; ?>" 
                             alt="头像" class="avatar-image">
                    </div>
                    
                    <div class="profile-info">
                        <h1 class="profile-name">
                            <?php echo escapeHtml($user['nickname'] ?? $user['full_name'] ?? $user['username']); ?>
                        </h1>
                        
                        <?php if ($user['signature']): ?>
                        <p class="profile-signature"><?php echo escapeHtml($user['signature']); ?></p>
                        <?php endif; ?>
                        
                        <?php if ($user['bio']): ?>
                        <p class="profile-bio"><?php echo nl2br(escapeHtml($user['bio'])); ?></p>
                        <?php endif; ?>
                        
                        <div class="profile-meta">
                            <?php if ($user['location']): ?>
                            <div class="meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span><?php echo escapeHtml($user['location']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($user['website']): ?>
                            <div class="meta-item">
                                <i class="fas fa-link"></i>
                                <a href="<?php echo escapeHtml($user['website']); ?>" target="_blank" rel="noopener">
                                    <?php echo escapeHtml($user['website']); ?>
                                </a>
                            </div>
                            <?php endif; ?>
                            
                            <div class="meta-item">
                                <i class="fas fa-calendar-alt"></i>
                                <span>加入于 <?php echo date('Y年m月', strtotime($user['created_at'])); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="profile-actions">
                        <?php if ($currentUser && $currentUser['id'] != $userId): ?>
                        <button class="btn <?php echo $isFollowing ? 'btn-outline' : 'btn-primary'; ?> follow-btn" 
                                data-user-id="<?php echo $userId; ?>">
                            <i class="fas <?php echo $isFollowing ? 'fa-user-minus' : 'fa-user-plus'; ?>"></i>
                            <?php echo $isFollowing ? '取消关注' : '关注'; ?>
                        </button>
                        <?php endif; ?>
                        
                        <?php if ($currentUser && $currentUser['id'] == $userId): ?>
                        <a href="edit-profile.php" class="btn btn-outline">
                            <i class="fas fa-edit"></i> 编辑资料
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- 统计信息 -->
                <div class="profile-stats">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo formatNumber($user['post_count'] ?? 0); ?></div>
                        <div class="stat-label">帖子</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-number"><?php echo formatNumber($user['comment_count'] ?? 0); ?></div>
                        <div class="stat-label">评论</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-number"><?php echo formatNumber($user['like_received_count'] ?? 0); ?></div>
                        <div class="stat-label">获赞</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-number"><?php echo formatNumber($followStats['followers_count'] ?? 0); ?></div>
                        <div class="stat-label">粉丝</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-number"><?php echo formatNumber($followStats['following_count'] ?? 0); ?></div>
                        <div class="stat-label">关注</div>
                    </div>
                </div>
            </div>

            <!-- 内容标签页 -->
            <div class="content-tabs">
                <div class="tab-nav">
                    <button class="tab-btn active" data-tab="posts">
                        <i class="fas fa-file-alt"></i> 帖子 (<?php echo $totalPosts; ?>)
                    </button>
                    <button class="tab-btn" data-tab="comments">
                        <i class="fas fa-comments"></i> 最新评论
                    </button>
                </div>
                
                <!-- 帖子列表 -->
                <div class="tab-content active" id="posts-tab">
                    <?php if (empty($posts)): ?>
                    <div class="empty-content">
                        <div class="empty-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <p>还没有发布任何帖子</p>
                    </div>
                    <?php else: ?>
                    <div class="posts-list">
                        <?php foreach ($posts as $post): ?>
                        <article class="post-item">
                            <div class="post-header">
                                <?php if ($post['category_name']): ?>
                                <span class="post-category" style="--category-color: <?php echo $post['category_color']; ?>">
                                    <?php echo escapeHtml($post['category_name']); ?>
                                </span>
                                <?php endif; ?>
                                
                                <div class="post-meta">
                                    <span class="post-time"><?php echo timeAgo($post['created_at']); ?></span>
                                </div>
                            </div>
                            
                            <h3 class="post-title">
                                <a href="community-post-detail.php?id=<?php echo $post['id']; ?>">
                                    <?php echo escapeHtml($post['title']); ?>
                                </a>
                            </h3>
                            
                            <?php if ($post['excerpt']): ?>
                            <p class="post-excerpt"><?php echo escapeHtml($post['excerpt']); ?></p>
                            <?php endif; ?>
                            
                            <div class="post-stats">
                                <div class="stat">
                                    <i class="fas fa-eye"></i>
                                    <span><?php echo formatNumber($post['view_count']); ?></span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-heart"></i>
                                    <span><?php echo formatNumber($post['like_count']); ?></span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-comment"></i>
                                    <span><?php echo formatNumber($post['comment_count']); ?></span>
                                </div>
                            </div>
                        </article>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- 分页 -->
                    <?php if ($totalPages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                        <a href="?id=<?php echo $userId; ?>&page=<?php echo $page - 1; ?>" class="page-btn">
                            <i class="fas fa-chevron-left"></i> 上一页
                        </a>
                        <?php endif; ?>
                        
                        <span class="page-info">
                            第 <?php echo $page; ?> 页，共 <?php echo $totalPages; ?> 页
                        </span>
                        
                        <?php if ($page < $totalPages): ?>
                        <a href="?id=<?php echo $userId; ?>&page=<?php echo $page + 1; ?>" class="page-btn">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>
                
                <!-- 评论列表 -->
                <div class="tab-content" id="comments-tab">
                    <?php if (empty($recentComments)): ?>
                    <div class="empty-content">
                        <div class="empty-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <p>还没有发表任何评论</p>
                    </div>
                    <?php else: ?>
                    <div class="comments-list">
                        <?php foreach ($recentComments as $comment): ?>
                        <div class="comment-item">
                            <div class="comment-header">
                                <div class="comment-post">
                                    评论于帖子：
                                    <a href="community-post-detail.php?id=<?php echo $comment['post_id']; ?>">
                                        <?php echo escapeHtml($comment['post_title']); ?>
                                    </a>
                                </div>
                                <div class="comment-time"><?php echo timeAgo($comment['created_at']); ?></div>
                            </div>
                            
                            <div class="comment-content">
                                <?php echo nl2br(escapeHtml($comment['content'])); ?>
                            </div>
                            
                            <div class="comment-stats">
                                <div class="stat">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span><?php echo formatNumber($comment['like_count']); ?></span>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <script src="assets/js/user-profile.js"></script>
</body>
</html>
