<?php
require_once 'includes/auth-init.php';

if (!$currentUser) {
    header('Location: admin-login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

try {
    $db = db();
    
    // 分页设置
    $postsPerPage = 15;
    $page = intval($_GET['page'] ?? 1);
    $offset = ($page - 1) * $postsPerPage;
    
    // 获取收藏的帖子
    $sql = "SELECT p.*, u.username, u.full_name, up.nickname, up.avatar_url,
                   pc.name as category_name, pc.slug as category_slug, pc.color as category_color,
                   b.created_at as bookmarked_at
            FROM bookmarks b
            LEFT JOIN posts p ON b.post_id = p.id
            LEFT JOIN users u ON p.user_id = u.id
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN post_categories pc ON p.category_id = pc.id
            WHERE b.user_id = ? AND p.status = 'published'
            ORDER BY b.created_at DESC
            LIMIT ? OFFSET ?";
    
    $bookmarks = $db->fetchAll($sql, [$currentUser['id'], $postsPerPage, $offset]);
    
    // 获取总收藏数
    $totalBookmarks = $db->fetchOne(
        "SELECT COUNT(*) as count FROM bookmarks b 
         LEFT JOIN posts p ON b.post_id = p.id 
         WHERE b.user_id = ? AND p.status = 'published'",
        [$currentUser['id']]
    )['count'];
    
    $totalPages = ceil($totalBookmarks / $postsPerPage);
    
} catch (Exception $e) {
    error_log("收藏页面错误: " . $e->getMessage());
    $bookmarks = [];
    $totalBookmarks = 0;
    $totalPages = 0;
}

// 辅助函数
function timeAgo($datetime) {
    if (empty($datetime)) {
        return '未知时间';
    }

    // 尝试多种时间格式解析
    $timestamp = false;

    // 首先尝试直接解析
    $timestamp = strtotime($datetime);

    // 如果失败，尝试其他格式
    if ($timestamp === false) {
        // 尝试ISO格式
        $timestamp = strtotime(str_replace('T', ' ', $datetime));
    }

    // 如果还是失败，尝试手动解析
    if ($timestamp === false) {
        // 匹配 YYYY-MM-DD HH:MM:SS 格式
        if (preg_match('/(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/', $datetime, $matches)) {
            $timestamp = mktime($matches[4], $matches[5], $matches[6], $matches[2], $matches[3], $matches[1]);
        }
    }

    if ($timestamp === false) {
        return '时间格式错误';
    }

    $currentTime = time();
    $time = $currentTime - $timestamp;

    // 如果时间差为负数（未来时间），可能是时区问题
    if ($time < 0) {
        $time = abs($time);
    }

    // 3分钟内显示"刚刚"
    if ($time < 180) return '刚刚';

    // 1小时内显示分钟
    if ($time < 3600) return floor($time/60) . '分钟前';

    // 24小时内显示小时
    if ($time < 86400) return floor($time/3600) . '小时前';

    // 30天内显示天数
    if ($time < 2592000) return floor($time/86400) . '天前';

    // 12个月内显示月数
    if ($time < 31536000) return floor($time/2592000) . '个月前';

    // 超过12个月显示年数
    return floor($time/31536000) . '年前';
}

function formatNumber($num) {
    if ($num >= 1000000) return round($num/1000000, 1) . 'M';
    if ($num >= 1000) return round($num/1000, 1) . 'K';
    return $num;
}

function escapeHtml($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的收藏 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/community.css">
    <link rel="stylesheet" href="assets/css/bookmarks.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.php">
                    <img src="assets/images/logo.png" alt="比特熊智慧系统" class="logo">
                    <span>比特熊智慧系统</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <a href="index.php" class="nav-link">首页</a>
                <a href="community.php" class="nav-link">社区</a>
                <a href="#" class="nav-link">课程</a>
                <a href="#" class="nav-link">文档</a>
            </div>
            
            <div class="nav-actions">
                <a href="community-post.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 发帖
                </a>
                <div class="user-menu">
                    <img src="<?php echo $currentUser['avatar'] ?? 'assets/images/default-avatar.png'; ?>" 
                         alt="头像" class="user-avatar">
                    <span><?php echo escapeHtml($currentUser['username']); ?></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 页面标题 -->
            <div class="page-header">
                <div class="breadcrumb">
                    <a href="community.php" class="breadcrumb-link">
                        <i class="fas fa-arrow-left"></i> 返回社区
                    </a>
                </div>
                
                <h1 class="page-title">
                    <i class="fas fa-bookmark"></i>
                    我的收藏
                </h1>
                
                <div class="page-stats">
                    <span class="stat-item">
                        <i class="fas fa-heart"></i>
                        共收藏了 <?php echo $totalBookmarks; ?> 个帖子
                    </span>
                </div>
            </div>

            <!-- 收藏列表 -->
            <div class="bookmarks-container">
                <?php if (empty($bookmarks)): ?>
                <div class="empty-bookmarks">
                    <div class="empty-icon">
                        <i class="fas fa-bookmark"></i>
                    </div>
                    <h3>还没有收藏任何帖子</h3>
                    <p>发现感兴趣的帖子时，点击收藏按钮保存到这里</p>
                    <a href="community.php" class="btn btn-primary">
                        <i class="fas fa-search"></i> 去发现好内容
                    </a>
                </div>
                <?php else: ?>
                <div class="bookmarks-list">
                    <?php foreach ($bookmarks as $bookmark): ?>
                    <article class="bookmark-item">
                        <div class="bookmark-header">
                            <div class="bookmark-meta">
                                <?php if ($bookmark['category_name']): ?>
                                <span class="post-category" style="--category-color: <?php echo $bookmark['category_color']; ?>">
                                    <?php echo escapeHtml($bookmark['category_name']); ?>
                                </span>
                                <?php endif; ?>
                                
                                <span class="bookmark-time">
                                    <i class="fas fa-bookmark"></i>
                                    收藏于 <?php echo timeAgo($bookmark['bookmarked_at']); ?>
                                </span>
                            </div>
                            
                            <button class="remove-bookmark-btn" data-post-id="<?php echo $bookmark['id']; ?>" 
                                    title="取消收藏">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        
                        <div class="bookmark-content">
                            <h3 class="bookmark-title">
                                <a href="community-post-detail.php?id=<?php echo $bookmark['id']; ?>">
                                    <?php echo escapeHtml($bookmark['title']); ?>
                                </a>
                            </h3>
                            
                            <?php if ($bookmark['excerpt']): ?>
                            <p class="bookmark-excerpt"><?php echo escapeHtml($bookmark['excerpt']); ?></p>
                            <?php endif; ?>
                            
                            <div class="bookmark-footer">
                                <div class="author-info">
                                    <a href="user-profile.php?id=<?php echo $bookmark['user_id']; ?>" class="author-link">
                                        <img src="<?php echo $bookmark['avatar_url'] ?? 'assets/images/default-avatar.png'; ?>" 
                                             alt="头像" class="author-avatar">
                                        <span class="author-name">
                                            <?php echo escapeHtml($bookmark['nickname'] ?? $bookmark['full_name'] ?? $bookmark['username']); ?>
                                        </span>
                                    </a>
                                    <span class="post-time">发布于 <?php echo timeAgo($bookmark['created_at']); ?></span>
                                </div>
                                
                                <div class="post-stats">
                                    <div class="stat">
                                        <i class="fas fa-eye"></i>
                                        <span><?php echo formatNumber($bookmark['view_count']); ?></span>
                                    </div>
                                    <div class="stat">
                                        <i class="fas fa-heart"></i>
                                        <span><?php echo formatNumber($bookmark['like_count']); ?></span>
                                    </div>
                                    <div class="stat">
                                        <i class="fas fa-comment"></i>
                                        <span><?php echo formatNumber($bookmark['comment_count']); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
                
                <!-- 分页 -->
                <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>" class="page-btn">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                    <?php endif; ?>
                    
                    <div class="page-numbers">
                        <?php
                        $start = max(1, $page - 2);
                        $end = min($totalPages, $page + 2);
                        
                        for ($i = $start; $i <= $end; $i++):
                        ?>
                        <a href="?page=<?php echo $i; ?>" 
                           class="page-number <?php echo $i == $page ? 'active' : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                        <?php endfor; ?>
                    </div>
                    
                    <?php if ($page < $totalPages): ?>
                    <a href="?page=<?php echo $page + 1; ?>" class="page-btn">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script src="assets/js/bookmarks.js"></script>
</body>
</html>
