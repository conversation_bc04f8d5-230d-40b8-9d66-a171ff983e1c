<?php
session_start();

// 检查是否已登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin-login.php');
    exit;
}

$admin_user = $_SESSION['admin_user'] ?? 'Admin';

// 引入数据库配置
$db_available = false;
$posts = [];
$categories = [];
$total_posts = 0;
$total_comments = 0;
$total_users = 0;

try {
    require_once 'config/database.php';
    $db = db();
    $db->initializeDatabase();
    $db_available = true;

    // 获取帖子数据
    $posts_sql = "SELECT p.*, u.username, u.full_name, up.nickname, up.avatar_url,
                         pc.name as category_name, pc.color as category_color,
                         (SELECT COUNT(*) FROM comments c WHERE c.post_id = p.id AND c.status != 'deleted') as comment_count
                  FROM posts p
                  LEFT JOIN users u ON p.user_id = u.id
                  LEFT JOIN user_profiles up ON u.id = up.user_id
                  LEFT JOIN post_categories pc ON p.category_id = pc.id
                  WHERE p.status != 'deleted'
                  ORDER BY p.is_pinned DESC, p.created_at DESC
                  LIMIT 50";

    $posts = $db->fetchAll($posts_sql);

    // 获取分类数据
    $categories = $db->fetchAll("SELECT * FROM post_categories ORDER BY sort_order");

    // 获取评论数据
    $comments_sql = "SELECT c.*, u.username, u.full_name, up.nickname, up.avatar_url,
                            p.title as post_title, p.id as post_id,
                            (SELECT COUNT(*) FROM reports r WHERE r.target_type = 'comment' AND r.target_id = c.id AND r.status = 'pending') as reports_count
                     FROM comments c
                     LEFT JOIN users u ON c.user_id = u.id
                     LEFT JOIN user_profiles up ON u.id = up.user_id
                     LEFT JOIN posts p ON c.post_id = p.id
                     WHERE c.status != 'deleted'
                     ORDER BY c.created_at DESC
                     LIMIT 50";

    $comments = $db->fetchAll($comments_sql);

    // 获取统计数据
    $stats = $db->fetchOne("SELECT
        (SELECT COUNT(*) FROM posts WHERE status != 'deleted') as total_posts,
        (SELECT COUNT(*) FROM comments WHERE status != 'deleted') as total_comments,
        (SELECT COUNT(*) FROM users WHERE status = 'active') as total_users");

    $total_posts = $stats['total_posts'] ?? 0;
    $total_comments = $stats['total_comments'] ?? 0;
    $total_users = $stats['total_users'] ?? 0;

} catch (Exception $e) {
    error_log("数据库初始化失败: " . $e->getMessage());
    // 数据库不可用时使用模拟数据
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比特熊智慧系统 - 管理后台</title>
    <link rel="icon" type="image/png" href="image/bit.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1e293b;
            overflow-x: hidden;
        }

        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 280px;
            background: linear-gradient(-70deg, rgb(20, 212, 216) 0%, rgb(0, 113, 235) 60%, rgb(0, 113, 235) 80%, rgb(142, 34, 167) 100%);
            color: white;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
        }

        .sidebar.collapsed {
            width: 80px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sidebar-logo {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .sidebar-logo img {
            width: 28px;
            height: 28px;
            border-radius: 50%;
        }

        .sidebar-title {
            font-size: 1.25rem;
            font-weight: 700;
            white-space: nowrap;
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .sidebar-title {
            opacity: 0;
        }

        .sidebar-toggle {
            position: absolute;
            top: 1.5rem;
            right: -15px;
            width: 30px;
            height: 30px;
            background: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            z-index: 1001;
        }

        .sidebar-toggle:hover {
            transform: scale(1.1);
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.875rem 1rem;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(4px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* 二级菜单样式 */
        .nav-item.has-submenu .nav-link {
            position: relative;
        }

        .nav-arrow {
            margin-left: auto;
            transition: transform 0.3s ease;
        }

        .nav-item.has-submenu.expanded .nav-arrow {
            transform: rotate(90deg);
        }

        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.1);
            margin-left: 1rem;
            border-radius: 8px;
            margin-top: 0.5rem;
        }

        .nav-item.has-submenu.expanded .submenu {
            max-height: 200px;
        }

        .submenu-item {
            padding: 0;
        }

        .submenu-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 6px;
            margin: 0.25rem;
        }

        .submenu-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .submenu-link.active {
            background: rgba(96, 165, 250, 0.3);
            color: white;
            border-left: 2px solid #60a5fa;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-text {
            font-weight: 500;
            white-space: nowrap;
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .nav-text {
            opacity: 0;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            transition: margin-left 0.3s ease;
        }

        .top-bar {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .notification-btn {
            position: relative;
            width: 40px;
            height: 40px;
            background: #f1f5f9;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .notification-btn:hover {
            background: #e2e8f0;
            transform: scale(1.05);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 18px;
            height: 18px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            width: 320px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            border: 1px solid #e2e8f0;
            z-index: 1000;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-dropdown.show {
            display: block;
        }

        .notification-header {
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .mark-all-read-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mark-all-read-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .notification-item {
            padding: 1rem;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .notification-item:hover {
            background: #f8fafc;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item.unread {
            background: #f0f9ff;
            border-left: 3px solid #3b82f6;
        }

        .notification-item.unread .notification-title {
            font-weight: 600;
        }

        .notification-title {
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }

        .notification-text {
            font-size: 0.875rem;
            color: #64748b;
            margin-bottom: 0.25rem;
        }

        .notification-time {
            font-size: 0.75rem;
            color: #94a3b8;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
        }

        .user-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        /* 用户下拉菜单 */
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            margin-top: 8px;
        }

        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .user-dropdown-header {
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            text-align: center;
        }

        .user-dropdown-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }

        .user-dropdown-role {
            font-size: 0.875rem;
            color: #64748b;
        }

        .user-dropdown-menu {
            padding: 0.5rem 0;
        }

        .user-dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .user-dropdown-item:hover {
            background: #f8fafc;
            color: #1e293b;
        }

        .user-dropdown-item.danger {
            color: #dc2626;
        }

        .user-dropdown-item.danger:hover {
            background: #fef2f2;
            color: #dc2626;
        }

        .user-dropdown-icon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        /* 页面内容样式 */
        .page-content {
            padding: 2rem;
            background: #f8fafc;
            min-height: calc(100vh - 80px);
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .page-header h2 {
            font-size: 1.875rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
        }

        /* 管理卡片样式 */
        .management-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .management-card .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .management-card .card-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        /* 搜索框样式 */
        .search-box {
            position: relative;
            width: 300px;
        }

        .search-box input {
            width: 100%;
            padding: 0.5rem 2.5rem 0.5rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
        }

        .search-box svg {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
        }

        /* 过滤标签样式 */
        .filter-tabs {
            display: flex;
            gap: 0.5rem;
        }

        .filter-tab {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-tab.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .filter-tab:hover:not(.active) {
            background: #f3f4f6;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
            font-size: 0.875rem;
        }

        .data-table td {
            color: #6b7280;
        }

        .data-table tr:hover {
            background: #f8fafc;
        }

        /* 徽章样式 */
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-primary {
            background: #dbeafe;
            color: #1e40af;
        }

        .badge-success {
            background: #dcfce7;
            color: #166534;
        }

        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .badge-danger {
            background: #fee2e2;
            color: #dc2626;
        }

        .badge-info {
            background: #e0f2fe;
            color: #0369a1;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-info {
            background: #06b6d4;
            color: white;
        }

        .btn-info:hover {
            background: #0891b2;
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
        }

        /* 帖子列表样式 */
        .post-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            padding: 1.5rem;
        }

        .post-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .post-content {
            flex: 1;
        }

        .post-content h4 {
            margin: 0 0 0.5rem 0;
            color: #1e293b;
            font-weight: 600;
        }

        .post-content p {
            margin: 0 0 1rem 0;
            color: #6b7280;
            line-height: 1.5;
        }

        .post-meta {
            display: flex;
            gap: 1rem;
            align-items: center;
            font-size: 0.875rem;
        }

        .post-actions {
            display: flex;
            gap: 0.5rem;
            margin-left: 1rem;
        }

        /* 公告样式 */
        .announcement-list {
            padding: 1.5rem;
        }

        .announcement-item {
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .announcement-item.important {
            border-color: #ef4444;
            background: #fef2f2;
        }

        .announcement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .announcement-header h4 {
            margin: 0;
            color: #1e293b;
            font-weight: 600;
        }

        .announcement-meta {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .announcement-content p {
            margin: 0 0 1rem 0;
            color: #6b7280;
            line-height: 1.6;
        }

        .announcement-actions {
            display: flex;
            gap: 0.5rem;
        }

        /* 邮箱样式 */
        .mailbox-nav {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .mailbox-nav .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 0.5rem;
        }

        .mailbox-nav .nav-item:hover {
            background: #f3f4f6;
        }

        .mailbox-nav .nav-item.active {
            background: #3b82f6;
            color: white;
        }

        .mailbox-nav .count {
            margin-left: auto;
            background: #ef4444;
            color: white;
            padding: 0.125rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
        }

        .mailbox-content {
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }

        .mailbox-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .toolbar-left {
            display: flex;
            gap: 0.5rem;
        }

        .search-input {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            width: 200px;
        }

        .mail-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .mail-item {
            display: grid;
            grid-template-columns: auto 150px 200px 1fr auto;
            gap: 1rem;
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            align-items: center;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .mail-item:hover {
            background: #f8fafc;
        }

        .mail-item.unread {
            background: #f0f9ff;
            font-weight: 600;
        }

        .mail-sender {
            font-weight: 500;
            color: #374151;
        }

        .mail-subject {
            font-weight: 500;
            color: #1e293b;
        }

        .mail-preview {
            color: #6b7280;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .mail-time {
            color: #9ca3af;
            font-size: 0.875rem;
        }

        /* 设置页面样式 */
        .settings-layout {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 2rem;
        }

        .settings-nav {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            height: fit-content;
        }

        .settings-nav-item {
            padding: 0.75rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 0.5rem;
        }

        .settings-nav-item:hover {
            background: #f3f4f6;
        }

        .settings-nav-item.active {
            background: #3b82f6;
            color: white;
        }

        .settings-content {
            background: white;
            border-radius: 8px;
            padding: 2rem;
        }

        .settings-section {
            display: none;
        }

        .settings-section.active {
            display: block;
        }

        .settings-section h3 {
            margin: 0 0 2rem 0;
            color: #1e293b;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 开关样式 */
        .toggle-switch {
            position: relative;
            display: inline-block;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-switch label {
            display: block;
            width: 50px;
            height: 24px;
            background: #d1d5db;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
            position: relative;
        }

        .toggle-switch label::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle-switch input:checked + label {
            background: #3b82f6;
        }

        .toggle-switch input:checked + label::after {
            transform: translateX(26px);
        }

        /* 管理模块快捷入口样式 */
        .management-shortcuts {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
            width: 100%;
            grid-column: 1 / -1; /* 占据整个网格宽度 */
        }

        .shortcuts-header {
            margin-bottom: 1.5rem;
        }

        .shortcuts-header h3 {
            margin: 0 0 0.5rem 0;
            color: #1e293b;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .shortcuts-subtitle {
            margin: 0;
            color: #64748b;
            font-size: 0.875rem;
        }

        .shortcuts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .shortcut-card {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .shortcut-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .shortcut-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #3b82f6;
        }

        .shortcut-card:hover::before {
            transform: scaleX(1);
        }

        .shortcut-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .shortcut-icon.announcement {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .shortcut-icon.community {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .shortcut-icon.mailbox {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        .shortcut-icon.app-management {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        /* 应用管理样式 */
        .app-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .app-card {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .app-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .app-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #3b82f6;
        }

        .app-card:hover::before {
            transform: scaleX(1);
        }

        .app-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .app-icon.community {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .app-icon.users {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        .app-icon.content {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .app-icon.settings {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        }

        .app-icon.analytics {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        .app-icon.health {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .app-icon.student-affairs {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .app-icon.backup {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .app-content {
            flex: 1;
        }

        .app-content h4 {
            margin: 0 0 0.25rem 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: #1e293b;
        }

        .app-content p {
            margin: 0 0 0.75rem 0;
            color: #64748b;
            font-size: 0.875rem;
        }

        .app-stats {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .app-arrow {
            color: #94a3b8;
            transition: all 0.3s ease;
        }

        .app-card:hover .app-arrow {
            color: #3b82f6;
            transform: translateX(4px);
        }

        .shortcut-content {
            flex: 1;
        }

        .shortcut-content h4 {
            margin: 0 0 0.5rem 0;
            color: #1e293b;
            font-size: 1.125rem;
            font-weight: 600;
        }

        .shortcut-content p {
            margin: 0 0 0.75rem 0;
            color: #64748b;
            font-size: 0.875rem;
            line-height: 1.4;
        }

        .shortcut-stats {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .stat-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background: #e2e8f0;
            color: #475569;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .stat-badge.important {
            background: #fee2e2;
            color: #dc2626;
        }

        .stat-badge.pending {
            background: #fef3c7;
            color: #d97706;
        }

        .shortcut-arrow {
            color: #94a3b8;
            transition: all 0.3s ease;
        }

        .shortcut-card:hover .shortcut-arrow {
            color: #3b82f6;
            transform: translateX(4px);
        }

        /* 返回按钮样式 */
        .back-button {
            margin-right: 1rem;
            background: #6b7280 !important;
            border: 1px solid #6b7280;
        }

        .back-button:hover {
            background: #4b5563 !important;
            border-color: #4b5563;
            transform: translateX(-2px);
        }

        .back-button svg {
            transition: transform 0.2s ease;
        }

        .back-button:hover svg {
            transform: translateX(-2px);
        }

        /* 导航栏管理样式 */
        .navbar-management-layout {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
        }

        .navbar-editor {
            min-height: 600px;
        }

        .navbar-tree {
            padding: 1rem;
        }

        .nav-tree-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .nav-tree-item:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .nav-tree-item.selected {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .nav-tree-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .nav-item-header {
            display: flex;
            align-items: center;
            padding: 1rem;
            gap: 0.75rem;
        }

        .nav-item-drag {
            cursor: grab;
            color: #9ca3af;
        }

        .nav-item-drag:active {
            cursor: grabbing;
        }

        .nav-item-icon {
            width: 20px;
            height: 20px;
            color: #6b7280;
        }

        .nav-item-content {
            flex: 1;
        }

        .nav-item-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }

        .nav-item-url {
            font-size: 0.875rem;
            color: #64748b;
        }

        .nav-item-actions {
            display: flex;
            gap: 0.5rem;
        }

        .nav-item-btn {
            padding: 0.5rem;
            border: none;
            background: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .nav-item-btn:hover {
            background: rgba(59, 130, 246, 0.1);
        }

        .nav-submenu {
            padding-left: 2rem;
            border-top: 1px solid #e2e8f0;
            background: #f1f5f9;
        }

        .nav-submenu .nav-tree-item {
            background: white;
            margin: 0.5rem 0;
        }

        /* 菜单编辑面板 */
        .menu-editor {
            padding: 1.5rem;
        }

        .no-selection {
            text-align: center;
            padding: 3rem 1rem;
            color: #64748b;
        }

        .no-selection-icon {
            margin-bottom: 1rem;
            color: #cbd5e1;
        }

        .no-selection h4 {
            margin: 0 0 0.5rem 0;
            color: #475569;
        }

        .menu-form {
            max-width: 100%;
        }

        .form-actions {
            display: flex;
            gap: 0.75rem;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e2e8f0;
        }

        .toggle-label {
            margin-left: 0.75rem;
            color: #374151;
            font-size: 0.875rem;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow: hidden;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 1.5rem;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #e2e8f0;
            display: flex;
            gap: 0.75rem;
            justify-content: flex-end;
            background: #f8fafc;
        }

        /* 预览模态框 */
        .navbar-preview-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }

        .navbar-preview-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 1200px;
            max-height: 90%;
            overflow: hidden;
            position: relative;
        }

        .preview-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .preview-body {
            padding: 0;
            height: 70vh;
            overflow: auto;
        }

        .preview-navbar {
            /* 这里会包含预览的导航栏样式 */
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .shortcuts-grid {
                grid-template-columns: 1fr;
            }

            .shortcut-card {
                padding: 1rem;
            }

            .shortcut-icon {
                width: 40px;
                height: 40px;
            }

            .back-button {
                margin-right: 0;
                margin-bottom: 1rem;
                width: 100%;
            }

            .navbar-management-layout {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .navbar-panel {
                order: -1;
            }

            .form-actions {
                flex-direction: column;
            }
        }

        .content-area {
            flex: 1;
            padding: 2rem;
            background: #f8fafc;
        }

        .dashboard-layout {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .dashboard-main {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .dashboard-sidebar {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
        }

        .dashboard-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .stat-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
        }

        .stat-value {
            font-weight: 600;
            color: #1e293b;
        }

        .calendar-container {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
        }

        .calendar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .calendar-nav {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .calendar-nav button {
            width: 32px;
            height: 32px;
            border: none;
            background: #f1f5f9;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .calendar-nav button:hover {
            background: #e2e8f0;
        }

        .calendar-month {
            font-weight: 600;
            color: #1e293b;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }

        .calendar-day {
            background: white;
            padding: 0.75rem 0.5rem;
            text-align: center;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .calendar-day:hover {
            background: #f8fafc;
        }

        .calendar-day.today {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
        }

        .calendar-day.has-event {
            background: #fef3c7;
        }

        .calendar-day.has-event::after {
            content: '';
            position: absolute;
            bottom: 4px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 4px;
            background: #f59e0b;
            border-radius: 50%;
        }

        .calendar-header-day {
            background: #f8fafc;
            padding: 0.5rem;
            text-align: center;
            font-size: 0.75rem;
            font-weight: 600;
            color: #64748b;
        }

        .activity-feed {
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .activity-icon.user {
            background: #dbeafe;
            color: #3b82f6;
        }

        .activity-icon.admin {
            background: #f3e8ff;
            color: #8b5cf6;
        }

        .activity-icon.system {
            background: #dcfce7;
            color: #16a34a;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }

        .activity-description {
            font-size: 0.875rem;
            color: #64748b;
            margin-bottom: 0.25rem;
        }

        .activity-time {
            font-size: 0.75rem;
            color: #94a3b8;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            backdrop-filter: blur(4px);
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
        }

        .modal-close {
            width: 32px;
            height: 32px;
            border: none;
            background: #f1f5f9;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: #e2e8f0;
            transform: scale(1.1);
        }

        .modal-body {
            padding: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            padding-top: 1rem;
            border-top: 1px solid #e2e8f0;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
        }

        .event-list {
            margin-top: 1rem;
        }

        .event-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
        }

        .event-item:hover {
            background: #f1f5f9;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .event-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }

        .event-time {
            font-size: 0.875rem;
            color: #64748b;
            margin-bottom: 0.5rem;
        }

        .event-description {
            font-size: 0.875rem;
            color: #64748b;
            margin-bottom: 0.75rem;
        }

        .event-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-small {
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #64748b;
        }

        .empty-state svg {
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -280px;
                height: 100vh;
                z-index: 1000;
            }

            .sidebar.open {
                left: 0;
            }

            .sidebar.collapsed {
                left: -280px;
            }

            .main-content {
                margin-left: 0;
            }

            .mobile-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
                display: none;
            }

            .mobile-overlay.show {
                display: block;
            }

            .top-bar {
                padding: 1rem;
            }

            .content-area {
                padding: 1rem;
            }
        }

        /* 主页设计器样式 */
        .homepage-designer-layout {
            display: block;
            max-width: 1000px;
            margin: 0 auto;
        }

        .designer-sidebar {
            width: 100%;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .section-tabs {
            display: flex;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .section-tab {
            flex: 1;
            padding: 12px 16px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            cursor: pointer;
            transition: all 0.2s;
            border-bottom: 2px solid transparent;
        }

        .section-tab.active {
            color: #3b82f6;
            background: white;
            border-bottom-color: #3b82f6;
        }

        .section-tab:hover:not(.active) {
            color: #475569;
            background: #f1f5f9;
        }

        .section-panel {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .section-panel h3 {
            margin-bottom: 20px;
            color: #1e293b;
            font-size: 18px;
            font-weight: 600;
        }

        /* O'Reilly区域样式 */
        .oreilly-tabs {
            display: flex;
            background: #f1f5f9;
            border-radius: 8px;
            margin-bottom: 24px;
            padding: 4px;
        }

        .oreilly-tab {
            flex: 1;
            padding: 8px 12px;
            background: none;
            border: none;
            font-size: 13px;
            color: #64748b;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s ease;
            text-align: center;
        }

        .oreilly-tab.active {
            color: #3b82f6;
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .oreilly-tab:hover:not(.active) {
            color: #475569;
            background: #e2e8f0;
        }

        .oreilly-content {
            display: none;
        }

        .oreilly-content.active {
            display: block;
        }

        .oreilly-content h4 {
            margin-bottom: 20px;
            color: #1e293b;
            font-size: 16px;
            font-weight: 600;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 8px;
        }

        .oreilly-content h5 {
            margin: 20px 0 12px 0;
            color: #475569;
            font-size: 14px;
            font-weight: 600;
        }

        .course-section {
            background: #f8fafc;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #e2e8f0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .file-upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            transition: all 0.2s;
            cursor: pointer;
            position: relative;
        }

        .file-upload-area:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .file-upload-text {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            color: #6b7280;
        }

        .file-upload-text svg {
            color: #9ca3af;
        }

        .current-image, .current-video {
            position: relative;
            margin-top: 16px;
        }

        .current-image img {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
        }

        .remove-image, .remove-video {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
        }



        .experts-list {
            margin-bottom: 20px;
        }

        .expert-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            position: relative;
        }

        .expert-item .remove-expert {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
        }

        .sections-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .section-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-info h4 {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .section-info p {
            font-size: 12px;
            color: #6b7280;
        }

        .section-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #d1d5db;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .toggle-switch.active {
            background: #3b82f6;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
        }

        .toggle-switch.active::after {
            transform: translateX(20px);
        }

        @media (max-width: 768px) {
            .homepage-designer-layout {
                margin: 0 1rem;
            }

            .designer-sidebar {
                border-radius: 8px;
            }
        }

        /* 社区管理样式 */
        .post-title-cell {
            max-width: 300px;
        }

        .post-title-cell strong {
            display: block;
            margin-bottom: 0.25rem;
            color: #1e293b;
        }

        .post-excerpt {
            font-size: 0.875rem;
            color: #64748b;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .user-avatar-small {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        .action-buttons {
            display: flex;
            gap: 0.25rem;
        }

        .table-container {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background-color: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        .table-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            background-color: #f8fafc;
            border-top: 1px solid #e2e8f0;
        }

        .bulk-actions {
            display: flex;
            gap: 0.5rem;
        }

        .pagination {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-info {
            color: #64748b;
            font-size: 0.875rem;
        }

        /* 评论管理样式 */
        .comments-list {
            padding: 1.5rem;
        }

        .comment-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #3b82f6;
        }

        .comment-item.pending {
            border-left-color: #f59e0b;
        }

        .comment-item.reported {
            border-left-color: #ef4444;
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .comment-user {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .user-details strong {
            display: block;
            color: #1e293b;
        }

        .user-role {
            font-size: 0.75rem;
            color: #64748b;
        }

        .comment-meta {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .comment-time {
            font-size: 0.875rem;
            color: #64748b;
        }

        .comment-content {
            margin-bottom: 1rem;
        }

        .comment-post-ref {
            font-size: 0.875rem;
            color: #64748b;
            margin-bottom: 0.5rem;
        }

        .comment-post-ref a {
            color: #3b82f6;
            text-decoration: none;
        }

        .comment-text {
            color: #374151;
            line-height: 1.5;
        }

        .report-info {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 0.75rem;
            margin-top: 0.75rem;
            font-size: 0.875rem;
            color: #991b1b;
        }

        .comment-actions {
            display: flex;
            gap: 0.5rem;
        }

        /* 分类管理样式 */
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            padding: 1.5rem;
        }

        .category-card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .category-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .category-card.disabled {
            opacity: 0.6;
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: #f8fafc;
        }

        .category-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .category-actions {
            display: flex;
            gap: 0.25rem;
        }

        .category-content {
            padding: 1rem;
        }

        .category-content h4 {
            margin: 0 0 0.5rem 0;
            color: #1e293b;
            font-size: 1.125rem;
        }

        .category-content p {
            color: #64748b;
            font-size: 0.875rem;
            margin: 0 0 1rem 0;
            line-height: 1.4;
        }

        .category-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            color: #64748b;
        }

        .category-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .category-order {
            font-size: 0.75rem;
            color: #64748b;
        }

        /* 统计页面样式 */
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .stats-content h3 {
            margin: 0 0 0.5rem 0;
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 500;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }

        .stats-change {
            font-size: 0.75rem;
            font-weight: 500;
        }

        .stats-change.positive {
            color: #059669;
        }

        .stats-change.negative {
            color: #dc2626;
        }

        .chart-container {
            padding: 1.5rem;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
        }

        .chart-controls {
            display: flex;
            gap: 0.5rem;
        }

        .hot-posts-list {
            padding: 1.5rem;
        }

        .hot-post-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 8px;
            margin-bottom: 0.75rem;
        }

        .post-rank {
            width: 32px;
            height: 32px;
            background: #3b82f6;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .post-info h4 {
            margin: 0 0 0.25rem 0;
            color: #1e293b;
            font-size: 1rem;
        }

        .post-stats {
            display: flex;
            gap: 1rem;
            font-size: 0.75rem;
            color: #64748b;
        }

        .category-stats-list {
            padding: 1.5rem;
        }

        .category-stat-item {
            margin-bottom: 1rem;
        }

        .category-name {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .category-progress {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #3b82f6;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.75rem;
            color: #64748b;
            min-width: 80px;
        }

        .user-activity-list {
            padding: 1.5rem;
        }

        .activity-user {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 8px;
            margin-bottom: 0.75rem;
        }

        .user-stats {
            font-size: 0.75rem;
            color: #64748b;
        }

        .quick-actions {
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .btn-block {
            width: 100%;
            justify-content: center;
        }

        /* 举报管理样式 */
        .reports-list {
            padding: 1.5rem;
        }

        .report-item {
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .report-item.urgent {
            border-left: 4px solid #ef4444;
            background: #fef2f2;
        }

        .report-item.processed {
            opacity: 0.7;
            background: #f0f9ff;
            border-left: 4px solid #3b82f6;
        }

        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .report-type {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .report-priority {
            font-size: 0.75rem;
            color: #64748b;
        }

        .report-content {
            margin-bottom: 1rem;
        }

        .report-target,
        .report-reason,
        .report-result {
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .report-target a {
            color: #3b82f6;
            text-decoration: none;
        }

        .report-details {
            display: flex;
            gap: 2rem;
            margin-top: 0.75rem;
            font-size: 0.875rem;
        }

        .reporter-info,
        .reported-user,
        .processor-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .report-actions {
            display: flex;
            gap: 0.5rem;
        }

        .deleted-content {
            color: #64748b;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 移动端遮罩层 -->
        <div class="mobile-overlay" id="mobileOverlay"></div>
        
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <img src="image/bit.png" alt="比特熊Logo">
                </div>
                <h2 class="sidebar-title">管理后台</h2>
            </div>
            
            <button class="sidebar-toggle" id="sidebarToggle">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>

            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="#" class="nav-link active" data-page="dashboard">
                        <div class="nav-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" rx="1"/>
                                <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" rx="1"/>
                                <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" rx="1"/>
                                <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" rx="1"/>
                            </svg>
                        </div>
                        <span class="nav-text">仪表板</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="user-management">
                        <div class="nav-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M17 21V19A4 4 0 0 0 13 15H5A4 4 0 0 0 1 19V21" stroke="currentColor" stroke-width="2"/>
                                <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                <path d="M23 21V19A4 4 0 0 0 16 15.13" stroke="currentColor" stroke-width="2"/>
                                <path d="M16 3.13A4 4 0 0 1 16 11" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">用户管理</span>
                    </a>
                </div>

                <div class="nav-item has-submenu">
                    <a href="#" class="nav-link" data-page="page-management">
                        <div class="nav-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <rect x="3" y="3" width="18" height="18" stroke="currentColor" stroke-width="2" rx="2"/>
                                <path d="M9 9H15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M9 12H15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M9 15H12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <span class="nav-text">页面管理</span>
                        <div class="nav-arrow">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                    </a>
                    <div class="submenu">
                        <div class="submenu-item">
                            <a href="#" class="submenu-link" data-page="page-list">
                                <span>页面列表</span>
                            </a>
                        </div>
                        <div class="submenu-item">
                            <a href="#" class="submenu-link" data-page="page-management">
                                <span>主页设计器</span>
                            </a>
                        </div>
                        <div class="submenu-item">
                            <a href="#" class="submenu-link" data-page="navbar-management">
                                <span>导航栏管理</span>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="app-management">
                        <div class="nav-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" rx="1"/>
                                <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" rx="1"/>
                                <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" rx="1"/>
                                <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" rx="1"/>
                            </svg>
                        </div>
                        <span class="nav-text">应用管理</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="system-settings">
                        <div class="nav-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                <path d="M19.4 15A1.65 1.65 0 0 0 20.25 13.5A8.5 8.5 0 0 0 12 4.75A8.5 8.5 0 0 0 3.75 13.5A1.65 1.65 0 0 0 4.6 15L12 12L19.4 15Z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">系统设置</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <h1 class="page-title" id="pageTitle">仪表板</h1>
                <div class="user-menu">
                    <div class="notification-btn" onclick="toggleNotifications()">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M18 8A6 6 0 0 0 6 8C6 15 3 17 3 17H21S18 15 18 8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M13.73 21A2 2 0 0 1 10.27 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span class="notification-badge" id="notificationBadge">3</span>

                        <!-- 通知下拉菜单 -->
                        <div class="notification-dropdown" id="notificationDropdown">
                            <div class="notification-header">
                                <span>通知中心</span>
                                <button class="mark-all-read-btn" onclick="markAllAsRead()">一键已读</button>
                            </div>
                            <div class="notification-list" id="notificationList">
                                <div class="notification-item unread" data-id="1">
                                    <div class="notification-title">新用户注册</div>
                                    <div class="notification-text">用户 "张三" 刚刚注册了账号</div>
                                    <div class="notification-time">2分钟前</div>
                                </div>
                                <div class="notification-item unread" data-id="2">
                                    <div class="notification-title">系统更新</div>
                                    <div class="notification-text">系统已成功更新到版本 v1.2.0</div>
                                    <div class="notification-time">1小时前</div>
                                </div>
                                <div class="notification-item unread" data-id="3">
                                    <div class="notification-title">服务器警告</div>
                                    <div class="notification-text">服务器CPU使用率达到85%</div>
                                    <div class="notification-time">3小时前</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="user-avatar" onclick="toggleUserMenu()">
                        <?php echo strtoupper(substr($admin_user, 0, 1)); ?>

                        <!-- 用户下拉菜单 -->
                        <div class="user-dropdown" id="userDropdown">
                            <div class="user-dropdown-header">
                                <div class="user-dropdown-name"><?php echo htmlspecialchars($admin_user); ?></div>
                                <div class="user-dropdown-role">
                                    <?php
                                    $role_name = '管理员';
                                    if (isset($_SESSION['user_type'])) {
                                        switch ($_SESSION['user_type']) {
                                            case 'super_admin':
                                                $role_name = '超级管理员';
                                                break;
                                            case 'admin':
                                                $role_name = '普通管理员';
                                                break;
                                            case 'super':
                                                $role_name = '超级管理员';
                                                break;
                                            case 'vip':
                                                $role_name = '特殊用户';
                                                break;
                                        }
                                    }
                                    echo $role_name;
                                    ?>
                                </div>
                            </div>
                            <div class="user-dropdown-menu">
                                <a href="#" class="user-dropdown-item" onclick="showProfile()">
                                    <div class="user-dropdown-icon">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <path d="M20 21V19A4 4 0 0 0 16 15H8A4 4 0 0 0 4 19V21" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    个人资料
                                </a>
                                <a href="#" class="user-dropdown-item" onclick="showSettings()">
                                    <div class="user-dropdown-icon">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                            <path d="M19.4 15A1.65 1.65 0 0 0 20.25 13.5A8.5 8.5 0 0 0 12 4.75A8.5 8.5 0 0 0 3.75 13.5A1.65 1.65 0 0 0 4.6 15L12 12L19.4 15Z" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    系统设置
                                </a>
                                <a href="admin-logout.php" class="user-dropdown-item danger" onclick="return confirmLogout()">
                                    <div class="user-dropdown-icon">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 21H5A2 2 0 0 1 3 19V5A2 2 0 0 1 5 3H9" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="16,17 21,12 16,7" stroke="currentColor" stroke-width="2"/>
                                            <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area" id="contentArea">
                <!-- 仪表板内容 -->
                <div id="dashboardContent">
                    <div class="dashboard-layout">
                        <!-- 主要内容区域 -->
                        <div class="dashboard-main">
                            <!-- 用户统计 -->
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3 class="card-title">用户数量统计</h3>
                                    <div class="card-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M17 21V19A4 4 0 0 0 13 15H5A4 4 0 0 0 1 19V21" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                            <path d="M23 21V19A4 4 0 0 0 16 15.13" stroke="currentColor" stroke-width="2"/>
                                            <path d="M16 3.13A4 4 0 0 1 16 11" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <span class="stat-label">超级管理员</span>
                                        <span class="stat-value">2</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">普通管理员</span>
                                        <span class="stat-value">8</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">高级用户</span>
                                        <span class="stat-value">156</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">普通用户</span>
                                        <span class="stat-value">2,847</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 系统信息 -->
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3 class="card-title">系统信息</h3>
                                    <div class="card-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                            <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/>
                                            <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <span class="stat-label">服务器</span>
                                        <span class="stat-value"><?php echo php_uname('s') . ' ' . php_uname('r'); ?></span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">PHP版本</span>
                                        <span class="stat-value"><?php echo PHP_VERSION; ?></span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">运行时间</span>
                                        <span class="stat-value" id="uptime"><?php
                                            $uptime = shell_exec('uptime -p 2>/dev/null') ?: '无法获取';
                                            echo trim(str_replace('up ', '', $uptime));
                                        ?></span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">内存使用</span>
                                        <span class="stat-value"><?php
                                            $memory_usage = memory_get_usage(true);
                                            $memory_peak = memory_get_peak_usage(true);
                                            echo round($memory_usage / 1024 / 1024, 1) . 'MB / ' . round($memory_peak / 1024 / 1024, 1) . 'MB';
                                        ?></span>
                                    </div>
                                </div>
                            </div>

                            <!-- 日历 -->
                            <div class="calendar-container">
                                <div class="calendar-header">
                                    <h3 class="card-title">日程安排</h3>
                                    <div class="calendar-nav">
                                        <button onclick="previousMonth()">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </button>
                                        <span class="calendar-month" id="calendarMonth">2025年1月</span>
                                        <button onclick="nextMonth()">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="calendar-grid" id="calendarGrid">
                                    <!-- 日历头部 -->
                                    <div class="calendar-header-day">日</div>
                                    <div class="calendar-header-day">一</div>
                                    <div class="calendar-header-day">二</div>
                                    <div class="calendar-header-day">三</div>
                                    <div class="calendar-header-day">四</div>
                                    <div class="calendar-header-day">五</div>
                                    <div class="calendar-header-day">六</div>
                                    <!-- 日历日期将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 侧边栏 -->
                        <div class="dashboard-sidebar">
                            <!-- 用户动态 -->
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3 class="card-title">用户动态</h3>
                                    <div class="card-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M22 12H18L15 21L9 3L6 12H2" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="activity-feed">
                                    <div class="activity-item">
                                        <div class="activity-icon user">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M20 21V19A4 4 0 0 0 16 15H8A4 4 0 0 0 4 19V21" stroke="currentColor" stroke-width="2"/>
                                                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">新用户注册</div>
                                            <div class="activity-description">用户 "李小明" 完成注册</div>
                                            <div class="activity-time">5分钟前</div>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon admin">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2"/>
                                                <path d="M21 12C21 16.97 16.97 21 12 21S3 16.97 3 12S7.03 3 12 3S21 7.03 21 12Z" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">管理员登录</div>
                                            <div class="activity-description">管理员 "admin" 登录系统</div>
                                            <div class="activity-time">15分钟前</div>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon user">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
                                                <path d="M14 2V8H20" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">申请处理</div>
                                            <div class="activity-description">处理了用户升级申请</div>
                                            <div class="activity-time">1小时前</div>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon system">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M12 22S8 18 8 14V5L12 3L16 5V14C16 18 12 22 12 22Z" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">系统更新</div>
                                            <div class="activity-description">系统已更新到最新版本</div>
                                            <div class="activity-time">2小时前</div>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon user">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M20 21V19A4 4 0 0 0 16 15H8A4 4 0 0 0 4 19V21" stroke="currentColor" stroke-width="2"/>
                                                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">用户注册</div>
                                            <div class="activity-description">用户 "王小红" 完成注册</div>
                                            <div class="activity-time">3小时前</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 管理模块快捷入口 -->
                    <div class="management-shortcuts">
                        <div class="shortcuts-header">
                            <h3 class="card-title">管理模块</h3>
                            <p class="shortcuts-subtitle">快速访问常用管理功能</p>
                        </div>

                        <div class="shortcuts-grid">
                            <!-- 公告栏入口 -->
                            <div class="shortcut-card" onclick="switchPage('announcement')">
                                <div class="shortcut-icon announcement">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2"/>
                                        <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2"/>
                                        <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </div>
                                <div class="shortcut-content">
                                    <h4>公告栏</h4>
                                    <p>发布和管理系统公告</p>
                                    <div class="shortcut-stats">
                                        <span class="stat-badge">3 条公告</span>
                                        <span class="stat-badge important">1 条重要</span>
                                    </div>
                                </div>
                                <div class="shortcut-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </div>
                            </div>

                            <!-- 应用管理入口 -->
                            <div class="shortcut-card" onclick="switchPage('app-management')">
                                <div class="shortcut-icon app-management">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" rx="1"/>
                                        <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" rx="1"/>
                                        <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" rx="1"/>
                                        <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" rx="1"/>
                                    </svg>
                                </div>
                                <div class="shortcut-content">
                                    <h4>应用管理</h4>
                                    <p>管理系统应用和功能模块</p>
                                    <div class="shortcut-stats">
                                        <span class="stat-badge">6 个应用</span>
                                        <span class="stat-badge active">全部运行中</span>
                                    </div>
                                </div>
                                <div class="shortcut-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </div>
                            </div>

                            <!-- 站内邮箱入口 -->
                            <div class="shortcut-card" onclick="switchPage('mailbox')">
                                <div class="shortcut-icon mailbox">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2"/>
                                        <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </div>
                                <div class="shortcut-content">
                                    <h4>站内邮箱</h4>
                                    <p>发送和接收站内消息</p>
                                    <div class="shortcut-stats">
                                        <span class="stat-badge">5 条未读</span>
                                        <span class="stat-badge">12 条消息</span>
                                    </div>
                                </div>
                                <div class="shortcut-arrow">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 页面设计器内容（初始隐藏） -->
                <div id="pageDesignerContent" style="display: none;">
                    <div class="dashboard-card">
                        <h3 class="card-title">页面设计器</h3>
                        <p class="card-description">这里是页面设计器模块，您可以在这里创建和编辑网站页面。</p>
                    </div>
                </div>
            </div>

            <!-- 应用管理页面 -->
            <div class="page-content" id="app-management" style="display: none;">
                <div class="page-header">
                    <h2>应用管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-secondary" onclick="refreshApps()">刷新状态</button>
                        <button class="btn btn-primary" onclick="showAddAppModal()">添加应用</button>
                    </div>
                </div>

                <div class="dashboard-layout">
                    <div class="dashboard-main">
                        <!-- 网站管理区域 -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3 class="card-title">网站管理</h3>
                                <div class="card-icon">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                        <path d="M2 12H22" stroke="currentColor" stroke-width="2"/>
                                        <path d="M12 2A15.3 15.3 0 0 1 16 12A15.3 15.3 0 0 1 12 22A15.3 15.3 0 0 1 8 12A15.3 15.3 0 0 1 12 2Z" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="app-grid">
                                <!-- 社区管理卡片 -->
                                <div class="app-card" onclick="openCommunityManagement()">
                                    <div class="app-icon community">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path d="M21 15A2 2 0 0 1 19 17H7L4 20V5A2 2 0 0 1 6 3H19A2 2 0 0 1 21 5Z" stroke="currentColor" stroke-width="2"/>
                                            <path d="M13 8H7" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                            <path d="M17 12H7" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                        </svg>
                                    </div>
                                    <div class="app-content">
                                        <h4>社区管理</h4>
                                        <p>管理用户帖子和互动</p>
                                        <div class="app-stats">
                                            <span class="stat-badge">156 个帖子</span>
                                            <span class="stat-badge pending">8 待审核</span>
                                        </div>
                                    </div>
                                    <div class="app-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                </div>

                                <!-- 用户管理卡片 -->
                                <div class="app-card" onclick="openUserManagement()">
                                    <div class="app-icon users">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path d="M17 21V19A4 4 0 0 0 13 15H5A4 4 0 0 0 1 19V21" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                            <path d="M23 21V19A4 4 0 0 0 16 15.13" stroke="currentColor" stroke-width="2"/>
                                            <path d="M16 3.13A4 4 0 0 1 16 11" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="app-content">
                                        <h4>用户管理</h4>
                                        <p>管理系统用户和权限</p>
                                        <div class="app-stats">
                                            <span class="stat-badge">1,234 用户</span>
                                            <span class="stat-badge active">89 在线</span>
                                        </div>
                                    </div>
                                    <div class="app-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                </div>

                                <!-- 内容管理卡片 -->
                                <div class="app-card" onclick="openContentManagement()">
                                    <div class="app-icon content">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <rect x="3" y="3" width="18" height="18" stroke="currentColor" stroke-width="2" rx="2"/>
                                            <path d="M9 9H15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                            <path d="M9 12H15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                            <path d="M9 15H12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                        </svg>
                                    </div>
                                    <div class="app-content">
                                        <h4>内容管理</h4>
                                        <p>管理网站页面和内容</p>
                                        <div class="app-stats">
                                            <span class="stat-badge">45 页面</span>
                                            <span class="stat-badge">12 草稿</span>
                                        </div>
                                    </div>
                                    <div class="app-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                </div>

                                <!-- 系统设置卡片 -->
                                <div class="app-card" onclick="openSystemSettings()">
                                    <div class="app-icon settings">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                            <path d="M19.4 15A1.65 1.65 0 0 0 20.25 13.5A8.5 8.5 0 0 0 12 4.75A8.5 8.5 0 0 0 3.75 13.5A1.65 1.65 0 0 0 4.6 15L12 12L19.4 15Z" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="app-content">
                                        <h4>系统设置</h4>
                                        <p>配置系统参数和选项</p>
                                        <div class="app-stats">
                                            <span class="stat-badge active">运行正常</span>
                                            <span class="stat-badge">v1.0.2</span>
                                        </div>
                                    </div>
                                    <div class="app-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                </div>

                                <!-- 数据统计卡片 -->
                                <div class="app-card" onclick="openDataAnalytics()">
                                    <div class="app-icon analytics">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path d="M3 3V21H21" stroke="currentColor" stroke-width="2"/>
                                            <path d="M9 9L12 6L16 10L20 6" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="app-content">
                                        <h4>数据统计</h4>
                                        <p>查看网站访问和使用统计</p>
                                        <div class="app-stats">
                                            <span class="stat-badge">12.5K 访问</span>
                                            <span class="stat-badge">+15%</span>
                                        </div>
                                    </div>
                                    <div class="app-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                </div>

                                <!-- 健康管理卡片 -->
                                <div class="app-card" onclick="openHealthManagement()">
                                    <div class="app-icon health">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path d="M22 12H18L15 21L9 3L6 12H2" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="app-content">
                                        <h4>健康管理</h4>
                                        <p>管理健康日志和统计分析</p>
                                        <div class="app-stats">
                                            <span class="stat-badge">14 个指标</span>
                                            <span class="stat-badge">本月</span>
                                        </div>
                                    </div>
                                    <div class="app-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                </div>

                                <!-- 学务管理卡片 -->
                                <div class="app-card" onclick="openStudentAffairs()">
                                    <div class="app-icon student-affairs">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path d="M22 10V6C22 4.9 21.1 4 20 4H4C2.9 4 2 4.9 2 6V10C3.1 10 4 10.9 4 12S3.1 14 2 14V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V14C20.9 14 20 13.1 20 12S20.9 10 22 10Z" stroke="currentColor" stroke-width="2"/>
                                            <path d="M13 16L9 12L13 8" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="app-content">
                                        <h4>学务管理</h4>
                                        <p>综合学习生活管理系统</p>
                                        <div class="app-stats">
                                            <span class="stat-badge">8 个模块</span>
                                            <span class="stat-badge">活跃</span>
                                        </div>
                                    </div>
                                    <div class="app-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                </div>

                                <!-- 备份管理卡片 -->
                                <div class="app-card" onclick="openBackupManagement()">
                                    <div class="app-icon backup">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                            <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                                            <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="app-content">
                                        <h4>备份管理</h4>
                                        <p>管理系统备份和恢复</p>
                                        <div class="app-stats">
                                            <span class="stat-badge">3 个备份</span>
                                            <span class="stat-badge">昨天</span>
                                        </div>
                                    </div>
                                    <div class="app-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户管理页面 -->
            <div class="page-content" id="user-management" style="display: none;">
                <div class="page-header">
                    <h2>用户管理</h2>
                    <button class="btn btn-primary" onclick="showAddUserModal()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        添加用户
                    </button>
                </div>

                <div class="management-card">
                    <div class="card-header">
                        <h3>用户列表</h3>
                        <div class="search-box">
                            <input type="text" placeholder="搜索用户..." id="userSearch">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                                <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>注册时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="userTableBody">
                                <tr>
                                    <td>1</td>
                                    <td>admin</td>
                                    <td><EMAIL></td>
                                    <td><span class="badge badge-primary">超级管理员</span></td>
                                    <td><span class="badge badge-success">活跃</span></td>
                                    <td>2025-01-01</td>
                                    <td>
                                        <button class="btn btn-small btn-secondary" onclick="editUser(1)">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteUser(1)">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>manager</td>
                                    <td><EMAIL></td>
                                    <td><span class="badge badge-info">普通管理员</span></td>
                                    <td><span class="badge badge-success">活跃</span></td>
                                    <td>2025-01-02</td>
                                    <td>
                                        <button class="btn btn-small btn-secondary" onclick="editUser(2)">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteUser(2)">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>


            <!-- 主页设计器 -->
            <div class="page-content" id="page-management" style="display: none;">
                <div class="homepage-designer-layout">
                    <!-- 配置面板 -->
                    <div class="designer-sidebar">
                        <div class="section-tabs">
                            <button class="section-tab active" data-section="hero">英雄区域</button>
                            <button class="section-tab" data-section="oreilly">O'Reilly区域</button>
                            <button class="section-tab" data-section="experts">专家团队</button>
                            <button class="section-tab" data-section="video">推荐视频</button>
                            <button class="section-tab" data-section="sections">区域设置</button>
                        </div>

                        <!-- 英雄区域编辑 -->
                        <div class="section-panel active" id="hero-panel">
                            <h3>英雄区域设置</h3>
                            <form id="heroForm">
                                <div class="form-group">
                                    <label for="heroTitle">主标题</label>
                                    <input type="text" id="heroTitle" name="title" class="form-control" placeholder="输入主标题">
                                </div>
                                <div class="form-group">
                                    <label for="heroSubtitle">副标题</label>
                                    <input type="text" id="heroSubtitle" name="subtitle" class="form-control" placeholder="输入副标题">
                                </div>
                                <div class="form-group">
                                    <label for="heroDescription">描述文本</label>
                                    <textarea id="heroDescription" name="description" class="form-control" rows="4" placeholder="输入描述文本"></textarea>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="primaryButtonText">主按钮文本</label>
                                        <input type="text" id="primaryButtonText" name="primary_button_text" class="form-control" placeholder="按钮文本">
                                    </div>
                                    <div class="form-group">
                                        <label for="primaryButtonUrl">主按钮链接</label>
                                        <input type="url" id="primaryButtonUrl" name="primary_button_url" class="form-control" placeholder="链接地址">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="secondaryButtonText">副按钮文本</label>
                                        <input type="text" id="secondaryButtonText" name="secondary_button_text" class="form-control" placeholder="按钮文本">
                                    </div>
                                    <div class="form-group">
                                        <label for="secondaryButtonUrl">副按钮链接</label>
                                        <input type="url" id="secondaryButtonUrl" name="secondary_button_url" class="form-control" placeholder="链接地址">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="heroImage">英雄区域图片</label>
                                    <div class="file-upload-area">
                                        <input type="file" id="heroImage" name="hero_image" accept="image/*" class="file-input">
                                        <div class="file-upload-text">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                <path d="M21 15V19A2 2 0 0 1 19 21H5A2 2 0 0 1 3 19V15" stroke="currentColor" stroke-width="2"/>
                                                <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                                                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                            <span>点击上传图片或拖拽到此处</span>
                                        </div>
                                        <div class="current-image" id="currentHeroImage" style="display: none;">
                                            <img src="" alt="当前图片">
                                            <button type="button" class="remove-image" onclick="removeHeroImage()">×</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group" style="margin-top: 24px;">
                                    <button type="button" class="btn btn-primary" onclick="saveHomepageContent()" style="width: 100%;">
                                        保存英雄区域设置
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- O'Reilly区域编辑 -->
                        <div class="section-panel" id="oreilly-panel" style="display: none;">
                            <div class="oreilly-tabs">
                                <button class="oreilly-tab active" data-oreilly-section="hero">技能建设区域</button>
                                <button class="oreilly-tab" data-oreilly-section="courses">专家课程区域</button>
                                <button class="oreilly-tab" data-oreilly-section="experts">知识分享区域</button>
                                <button class="oreilly-tab" data-oreilly-section="testimonial">推荐区域</button>
                                <button class="oreilly-tab" data-oreilly-section="cta">帮助团队区域</button>
                            </div>

                            <!-- O'Reilly英雄区域 (技能建设区域) -->
                            <div class="oreilly-content active" id="oreilly-hero-content">
                                <h4>技能建设区域设置</h4>
                                <form id="oreillyHeroForm">
                                    <div class="form-group">
                                        <label for="oreillyHeroTitle">主标题 (H1级别)</label>
                                        <input type="text" id="oreillyHeroTitle" class="form-control" placeholder="输入主标题">
                                    </div>
                                    <div class="form-group">
                                        <label for="oreillyHeroDescription">描述文本</label>
                                        <textarea id="oreillyHeroDescription" class="form-control" rows="3" placeholder="输入描述文本"></textarea>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="oreillyHeroPrimaryBtnText">主按钮文本</label>
                                            <input type="text" id="oreillyHeroPrimaryBtnText" class="form-control" placeholder="主按钮文本">
                                        </div>
                                        <div class="form-group">
                                            <label for="oreillyHeroPrimaryBtnUrl">主按钮链接</label>
                                            <input type="url" id="oreillyHeroPrimaryBtnUrl" class="form-control" placeholder="主按钮链接">
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="oreillyHeroSecondaryBtnText">次按钮文本</label>
                                            <input type="text" id="oreillyHeroSecondaryBtnText" class="form-control" placeholder="次按钮文本">
                                        </div>
                                        <div class="form-group">
                                            <label for="oreillyHeroSecondaryBtnUrl">次按钮链接</label>
                                            <input type="url" id="oreillyHeroSecondaryBtnUrl" class="form-control" placeholder="次按钮链接">
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="saveOreillyHero()">保存技能建设区域</button>
                                </form>
                            </div>

                            <!-- 专家课程区域 -->
                            <div class="oreilly-content" id="oreilly-courses-content" style="display: none;">
                                <h4>专家课程区域设置</h4>

                                <!-- 专家指导课程 -->
                                <div class="course-section">
                                    <h5>专家指导课程 (H3级别)</h5>
                                    <form id="liveCoursesForm">
                                        <div class="form-group">
                                            <label for="liveCoursesTitle">标题</label>
                                            <input type="text" id="liveCoursesTitle" class="form-control" placeholder="输入标题">
                                        </div>
                                        <div class="form-group">
                                            <label for="liveCoursesDescription">描述</label>
                                            <textarea id="liveCoursesDescription" class="form-control" rows="2" placeholder="输入描述"></textarea>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="liveCoursesButtonText">按钮文本</label>
                                                <input type="text" id="liveCoursesButtonText" class="form-control" placeholder="按钮文本">
                                            </div>
                                            <div class="form-group">
                                                <label for="liveCoursesButtonUrl">按钮链接</label>
                                                <input type="url" id="liveCoursesButtonUrl" class="form-control" placeholder="按钮链接">
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <!-- AI智能答案 -->
                                <div class="course-section">
                                    <h5>AI智能答案 (H3级别)</h5>
                                    <form id="aiAnswersForm">
                                        <div class="form-group">
                                            <label for="aiAnswersTitle">标题</label>
                                            <input type="text" id="aiAnswersTitle" class="form-control" placeholder="输入标题">
                                        </div>
                                        <div class="form-group">
                                            <label for="aiAnswersDescription">描述</label>
                                            <textarea id="aiAnswersDescription" class="form-control" rows="2" placeholder="输入描述"></textarea>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="aiAnswersButtonText">按钮文本</label>
                                                <input type="text" id="aiAnswersButtonText" class="form-control" placeholder="按钮文本">
                                            </div>
                                            <div class="form-group">
                                                <label for="aiAnswersButtonUrl">按钮链接</label>
                                                <input type="url" id="aiAnswersButtonUrl" class="form-control" placeholder="按钮链接">
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <button type="button" class="btn btn-primary" onclick="saveOreillyCourses()">保存专家课程区域</button>
                            </div>

                            <!-- 知识分享区域 -->
                            <div class="oreilly-content" id="oreilly-experts-content" style="display: none;">
                                <h4>知识分享区域设置</h4>
                                <form id="oreillyExpertsForm">
                                    <div class="form-group">
                                        <label for="oreillyExpertsTitle">主标题 (H2级别)</label>
                                        <input type="text" id="oreillyExpertsTitle" class="form-control" placeholder="输入主标题">
                                    </div>
                                    <div class="form-group">
                                        <label for="oreillyExpertsDescription">描述文本</label>
                                        <textarea id="oreillyExpertsDescription" class="form-control" rows="4" placeholder="输入描述文本"></textarea>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="saveOreillyExperts()">保存知识分享区域</button>
                                </form>
                            </div>

                            <!-- 推荐区域 -->
                            <div class="oreilly-content" id="oreilly-testimonial-content" style="display: none;">
                                <h4>推荐区域设置</h4>
                                <form id="oreillyTestimonialForm">
                                    <div class="form-group">
                                        <label for="testimonialTitle">推荐标题 (H3级别)</label>
                                        <input type="text" id="testimonialTitle" class="form-control" placeholder="输入推荐标题">
                                    </div>
                                    <div class="form-group">
                                        <label for="testimonialDescription">推荐描述</label>
                                        <textarea id="testimonialDescription" class="form-control" rows="2" placeholder="输入推荐描述"></textarea>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="stat1Number">统计数字1</label>
                                            <input type="text" id="stat1Number" class="form-control" placeholder="5+">
                                        </div>
                                        <div class="form-group">
                                            <label for="stat1Label">统计标签1</label>
                                            <input type="text" id="stat1Label" class="form-control" placeholder="Years using">
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="stat2Number">统计数字2</label>
                                            <input type="text" id="stat2Number" class="form-control" placeholder="200+">
                                        </div>
                                        <div class="form-group">
                                            <label for="stat2Label">统计标签2</label>
                                            <input type="text" id="stat2Label" class="form-control" placeholder="Books read">
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="stat3Number">统计数字3</label>
                                            <input type="text" id="stat3Number" class="form-control" placeholder="50+">
                                        </div>
                                        <div class="form-group">
                                            <label for="stat3Label">统计标签3</label>
                                            <input type="text" id="stat3Label" class="form-control" placeholder="Courses completed">
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="testimonialButtonText">按钮文本</label>
                                            <input type="text" id="testimonialButtonText" class="form-control" placeholder="按钮文本">
                                        </div>
                                        <div class="form-group">
                                            <label for="testimonialButtonUrl">按钮链接</label>
                                            <input type="url" id="testimonialButtonUrl" class="form-control" placeholder="按钮链接">
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="saveOreillyTestimonial()">保存推荐区域</button>
                                </form>
                            </div>

                            <!-- 帮助团队区域 -->
                            <div class="oreilly-content" id="oreilly-cta-content" style="display: none;">
                                <h4>帮助团队区域设置</h4>
                                <form id="oreillyCtaForm">
                                    <div class="form-group">
                                        <label for="oreillyCtaTitle">主标题 (H2级别)</label>
                                        <input type="text" id="oreillyCtaTitle" class="form-control" placeholder="输入主标题">
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="oreillyCtaPrimaryBtnText">主按钮文本</label>
                                            <input type="text" id="oreillyCtaPrimaryBtnText" class="form-control" placeholder="主按钮文本">
                                        </div>
                                        <div class="form-group">
                                            <label for="oreillyCtaPrimaryBtnUrl">主按钮链接</label>
                                            <input type="url" id="oreillyCtaPrimaryBtnUrl" class="form-control" placeholder="主按钮链接">
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="oreillyCtaSecondaryBtnText">次按钮文本</label>
                                            <input type="text" id="oreillyCtaSecondaryBtnText" class="form-control" placeholder="次按钮文本">
                                        </div>
                                        <div class="form-group">
                                            <label for="oreillyCtaSecondaryBtnUrl">次按钮链接</label>
                                            <input type="url" id="oreillyCtaSecondaryBtnUrl" class="form-control" placeholder="次按钮链接">
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="saveOreillyCta()">保存帮助团队区域</button>
                                </form>
                            </div>
                        </div>

                        <!-- 专家团队编辑 -->
                        <div class="section-panel" id="experts-panel" style="display: none;">
                            <h3>专家团队管理</h3>
                            <div class="experts-list" id="expertsList">
                                <!-- 专家列表将通过JavaScript动态生成 -->
                            </div>
                            <button class="btn btn-outline" onclick="addNewExpert()">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                添加专家
                            </button>
                        </div>

                        <!-- 推荐视频编辑 -->
                        <div class="section-panel" id="video-panel" style="display: none;">
                            <h3>推荐视频设置</h3>
                            <form id="videoForm">
                                <div class="form-group">
                                    <label for="videoTitle">视频标题</label>
                                    <input type="text" id="videoTitle" name="title" class="form-control" placeholder="输入视频标题">
                                </div>
                                <div class="form-group">
                                    <label for="videoDescription">视频描述</label>
                                    <textarea id="videoDescription" name="description" class="form-control" rows="3" placeholder="输入视频描述"></textarea>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="speakerName">演讲者姓名</label>
                                        <input type="text" id="speakerName" name="speaker_name" class="form-control" placeholder="演讲者姓名">
                                    </div>
                                    <div class="form-group">
                                        <label for="speakerRole">演讲者职位</label>
                                        <input type="text" id="speakerRole" name="speaker_role" class="form-control" placeholder="演讲者职位">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="companyBadge">公司标识</label>
                                    <input type="text" id="companyBadge" name="company_badge" class="form-control" placeholder="公司名称">
                                </div>
                                <div class="form-group">
                                    <label for="videoFile">视频文件</label>
                                    <div class="file-upload-area">
                                        <input type="file" id="videoFile" name="video_file" accept="video/*" class="file-input">
                                        <div class="file-upload-text">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                <polygon points="23,7 16,12 23,17" stroke="currentColor" stroke-width="2" fill="none"/>
                                                <rect x="1" y="5" width="15" height="14" stroke="currentColor" stroke-width="2" rx="2" ry="2"/>
                                            </svg>
                                            <span>点击上传视频或拖拽到此处</span>
                                        </div>
                                        <div class="current-video" id="currentVideo" style="display: none;">
                                            <video controls width="100%">
                                                <source src="" type="video/mp4">
                                            </video>
                                            <button type="button" class="remove-video" onclick="removeVideo()">×</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group" style="margin-top: 24px;">
                                    <button type="button" class="btn btn-primary" onclick="saveHomepageContent()" style="width: 100%;">
                                        保存视频设置
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- 区域设置 -->
                        <div class="section-panel" id="sections-panel" style="display: none;">
                            <h3>页面区域设置</h3>
                            <div class="sections-list" id="sectionsList">
                                <!-- 区域列表将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 导航栏管理 -->
            <div class="page-content" id="navbar-management" style="display: none;">
                <div class="page-header">
                    <h2>导航栏管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-secondary" onclick="previewNavbar()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            预览导航栏
                        </button>
                        <button class="btn btn-info" onclick="refreshNavbar()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M23 4V10H17" stroke="currentColor" stroke-width="2"/>
                                <path d="M20.49 15A9 9 0 1 1 5.64 5.64L9 9" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            刷新
                        </button>
                        <button class="btn btn-primary" onclick="addNavItem()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            添加菜单项
                        </button>
                    </div>
                </div>

                <div class="navbar-management-layout">
                    <!-- 左侧：导航栏结构编辑 -->
                    <div class="navbar-editor">
                        <div class="management-card">
                            <div class="card-header">
                                <h3>导航栏结构</h3>
                                <p class="card-description">拖拽调整菜单顺序，点击编辑菜单项</p>
                            </div>

                            <div class="navbar-tree" id="navbarTree">
                                <!-- 导航栏菜单项将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：菜单项编辑面板 -->
                    <div class="navbar-panel">
                        <div class="management-card">
                            <div class="card-header">
                                <h3>菜单项编辑</h3>
                                <p class="card-description">编辑选中的菜单项属性</p>
                            </div>

                            <div class="menu-editor" id="menuEditor">
                                <div class="no-selection">
                                    <div class="no-selection-icon">
                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2"/>
                                            <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <h4>选择菜单项</h4>
                                    <p>点击左侧的菜单项来编辑其属性</p>
                                </div>

                                <div class="menu-form" id="menuForm" style="display: none;">
                                    <div class="form-group">
                                        <label>菜单名称</label>
                                        <input type="text" id="menuName" class="form-input" placeholder="输入菜单名称">
                                    </div>

                                    <div class="form-group">
                                        <label>菜单链接</label>
                                        <input type="text" id="menuUrl" class="form-input" placeholder="输入菜单链接 (如: /about.php)">
                                    </div>

                                    <div class="form-group">
                                        <label>菜单图标 (可选)</label>
                                        <input type="text" id="menuIcon" class="form-input" placeholder="输入图标类名或SVG">
                                    </div>

                                    <div class="form-group">
                                        <label>菜单类型</label>
                                        <select id="menuType" class="form-input">
                                            <option value="link">普通链接</option>
                                            <option value="dropdown">下拉菜单</option>
                                            <option value="submenu">子菜单项</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label>显示状态</label>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="menuVisible" checked>
                                            <label for="menuVisible"></label>
                                        </div>
                                        <span class="toggle-label">显示此菜单项</span>
                                    </div>

                                    <div class="form-group">
                                        <label>排序权重</label>
                                        <input type="number" id="menuOrder" class="form-input" value="0" placeholder="数字越小越靠前">
                                    </div>

                                    <div class="form-actions">
                                        <button class="btn btn-success" onclick="saveMenuItem()">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                            保存
                                        </button>
                                        <button class="btn btn-danger" onclick="deleteMenuItem()">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                                                <path d="M19 6V20A2 2 0 0 1 17 22H7A2 2 0 0 1 5 20V6M8 6V4A2 2 0 0 1 10 2H14A2 2 0 0 1 16 4V6" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                            删除
                                        </button>
                                        <button class="btn btn-secondary" onclick="cancelEdit()">取消</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>





            <!-- 添加菜单项模态框 -->
            <div class="modal" id="addNavItemModal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>添加菜单项</h3>
                        <button class="modal-close" onclick="closeAddNavItemModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="addNavItemForm">
                            <div class="form-group">
                                <label>菜单名称 *</label>
                                <input type="text" id="addMenuName" class="form-input" placeholder="输入菜单名称" required>
                            </div>

                            <div class="form-group">
                                <label>菜单链接 *</label>
                                <input type="text" id="addMenuUrl" class="form-input" placeholder="输入菜单链接 (如: /about.php)" required>
                            </div>

                            <div class="form-group">
                                <label>菜单类型</label>
                                <select id="addMenuType" class="form-input" onchange="handleMenuTypeChange()">
                                    <option value="link">普通链接</option>
                                    <option value="dropdown">下拉菜单</option>
                                    <option value="submenu">子菜单项</option>
                                </select>
                            </div>

                            <div class="form-group" id="addParentGroup" style="display: none;">
                                <label>父菜单</label>
                                <select id="addParentMenu" class="form-input">
                                    <option value="">选择父菜单</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>菜单图标 (可选)</label>
                                <input type="text" id="addMenuIcon" class="form-input" placeholder="输入图标类名或SVG">
                            </div>

                            <div class="form-group">
                                <label>排序权重</label>
                                <input type="number" id="addMenuOrder" class="form-input" value="0" placeholder="数字越小越靠前">
                            </div>

                            <div class="form-group">
                                <label>显示状态</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="addMenuVisible" checked>
                                    <label for="addMenuVisible"></label>
                                </div>
                                <span class="toggle-label">显示此菜单项</span>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeAddNavItemModal()">取消</button>
                        <button class="btn btn-primary" onclick="submitAddNavItem()">添加菜单项</button>
                    </div>
                </div>
            </div>

            <!-- 添加帖子模态框 -->
            <div class="modal" id="addPostModal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>发布新帖子</h3>
                        <button class="modal-close" onclick="closeModal('addPostModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="addPostForm">
                            <div class="form-group">
                                <label>帖子标题 *</label>
                                <input type="text" id="postTitle" class="form-input" placeholder="输入帖子标题" required>
                            </div>
                            <div class="form-group">
                                <label>帖子分类 *</label>
                                <select id="postCategory" class="form-input" required>
                                    <option value="">选择分类</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>帖子内容 *</label>
                                <textarea id="postContent" class="form-input" rows="8" placeholder="输入帖子内容" required></textarea>
                            </div>
                            <div class="form-group">
                                <label>标签</label>
                                <input type="text" id="postTags" class="form-input" placeholder="输入标签，用逗号分隔">
                            </div>
                            <div class="form-group">
                                <div class="toggle-group">
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="postVisible" checked>
                                        <label for="postVisible"></label>
                                    </div>
                                    <span class="toggle-label">立即发布</span>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal('addPostModal')">取消</button>
                        <button class="btn btn-primary" onclick="submitAddPost()">发布帖子</button>
                    </div>
                </div>
            </div>

            <!-- 添加分类模态框 -->
            <div class="modal" id="addCategoryModal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>添加新分类</h3>
                        <button class="modal-close" onclick="closeModal('addCategoryModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="addCategoryForm">
                            <div class="form-group">
                                <label>分类名称 *</label>
                                <input type="text" id="categoryName" class="form-input" placeholder="输入分类名称" required>
                            </div>
                            <div class="form-group">
                                <label>分类描述</label>
                                <textarea id="categoryDescription" class="form-input" rows="3" placeholder="输入分类描述"></textarea>
                            </div>
                            <div class="form-group">
                                <label>分类图标颜色</label>
                                <select id="categoryColor" class="form-input">
                                    <option value="blue">蓝色</option>
                                    <option value="green">绿色</option>
                                    <option value="orange">橙色</option>
                                    <option value="purple">紫色</option>
                                    <option value="red">红色</option>
                                    <option value="gray">灰色</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>排序顺序</label>
                                <input type="number" id="categoryOrder" class="form-input" placeholder="输入排序数字" min="1">
                            </div>
                            <div class="form-group">
                                <div class="toggle-group">
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="categoryEnabled" checked>
                                        <label for="categoryEnabled"></label>
                                    </div>
                                    <span class="toggle-label">启用此分类</span>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal('addCategoryModal')">取消</button>
                        <button class="btn btn-primary" onclick="submitAddCategory()">添加分类</button>
                    </div>
                </div>
            </div>











            <!-- 公告栏 -->
            <div class="page-content" id="announcement" style="display: none;">
                <div class="page-header">
                    <h2>公告栏管理</h2>
                    <button class="btn btn-primary" onclick="showAddAnnouncementModal()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        发布公告
                    </button>
                </div>

                <div class="management-card">
                    <div class="card-header">
                        <h3>公告列表</h3>
                        <div class="filter-tabs">
                            <button class="filter-tab active" data-filter="all">全部</button>
                            <button class="filter-tab" data-filter="important">重要</button>
                            <button class="filter-tab" data-filter="normal">普通</button>
                            <button class="filter-tab" data-filter="expired">已过期</button>
                        </div>
                    </div>

                    <div class="announcement-list">
                        <div class="announcement-item important">
                            <div class="announcement-header">
                                <h4>系统维护通知</h4>
                                <div class="announcement-meta">
                                    <span class="badge badge-danger">重要</span>
                                    <span class="time">2025-01-15</span>
                                </div>
                            </div>
                            <div class="announcement-content">
                                <p>系统将于2025年1月20日凌晨2:00-4:00进行维护升级，期间可能无法正常访问，请提前做好准备。</p>
                            </div>
                            <div class="announcement-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-info">置顶</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>

                        <div class="announcement-item">
                            <div class="announcement-header">
                                <h4>新功能上线</h4>
                                <div class="announcement-meta">
                                    <span class="badge badge-info">普通</span>
                                    <span class="time">2025-01-10</span>
                                </div>
                            </div>
                            <div class="announcement-content">
                                <p>我们很高兴地宣布，新的日程管理功能已经上线，用户可以更方便地管理自己的学习计划。</p>
                            </div>
                            <div class="announcement-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-warning">取消置顶</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 站内邮箱 -->
            <div class="page-content" id="mailbox" style="display: none;">
                <div class="page-header">
                    <h2>站内邮箱</h2>
                    <button class="btn btn-primary" onclick="showComposeModal()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M3 3H21C21.5523 3 22 3.44772 22 4V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4C2 3.44772 2.44772 3 3 3Z" stroke="currentColor" stroke-width="2"/>
                            <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        写邮件
                    </button>
                </div>

                <div class="dashboard-layout">
                    <div class="dashboard-sidebar" style="width: 250px;">
                        <div class="mailbox-nav">
                            <div class="nav-item active" data-folder="inbox">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <polyline points="22,6 16,12 14,10" stroke="currentColor" stroke-width="2"/>
                                    <path d="M22 6L12 13L2 6" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                收件箱 <span class="count">5</span>
                            </div>
                            <div class="nav-item" data-folder="sent">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <line x1="22" y1="2" x2="11" y2="13" stroke="currentColor" stroke-width="2"/>
                                    <polygon points="22,2 15,22 11,13 2,9 22,2" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                已发送
                            </div>
                            <div class="nav-item" data-folder="draft">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M11 4H4A2 2 0 0 0 2 6V18A2 2 0 0 0 4 20H16A2 2 0 0 0 18 18V13" stroke="currentColor" stroke-width="2"/>
                                    <path d="M18.5 2.5A2.121 2.121 0 0 1 21 5L12 14L8 15L9 11L18.5 2.5Z" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                草稿箱 <span class="count">2</span>
                            </div>
                            <div class="nav-item" data-folder="trash">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                                    <path d="M19 6V20A2 2 0 0 1 17 22H7A2 2 0 0 1 5 20V6M8 6V4A2 2 0 0 1 10 2H14A2 2 0 0 1 16 4V6" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                回收站
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-main">
                        <div class="mailbox-content">
                            <div class="mailbox-toolbar">
                                <div class="toolbar-left">
                                    <button class="btn btn-small btn-secondary">全选</button>
                                    <button class="btn btn-small btn-danger">删除</button>
                                    <button class="btn btn-small btn-info">标记已读</button>
                                </div>
                                <div class="toolbar-right">
                                    <input type="text" placeholder="搜索邮件..." class="search-input">
                                </div>
                            </div>

                            <div class="mail-list">
                                <div class="mail-item unread">
                                    <div class="mail-checkbox">
                                        <input type="checkbox">
                                    </div>
                                    <div class="mail-sender">系统通知</div>
                                    <div class="mail-subject">欢迎使用比特熊智慧系统</div>
                                    <div class="mail-preview">感谢您注册使用我们的系统，这里是一些使用指南...</div>
                                    <div class="mail-time">2小时前</div>
                                </div>

                                <div class="mail-item">
                                    <div class="mail-checkbox">
                                        <input type="checkbox">
                                    </div>
                                    <div class="mail-sender">管理员</div>
                                    <div class="mail-subject">系统维护通知</div>
                                    <div class="mail-preview">系统将于本周末进行例行维护，请提前保存您的工作...</div>
                                    <div class="mail-time">1天前</div>
                                </div>

                                <div class="mail-item">
                                    <div class="mail-checkbox">
                                        <input type="checkbox">
                                    </div>
                                    <div class="mail-sender">用户支持</div>
                                    <div class="mail-subject">您的问题已解决</div>
                                    <div class="mail-preview">关于您提交的技术支持请求，我们已经为您解决了...</div>
                                    <div class="mail-time">3天前</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统设置 -->
            <div class="page-content" id="system-settings" style="display: none;">
                <div class="page-header">
                    <h2>系统设置</h2>
                    <button class="btn btn-success" onclick="saveSettings()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        保存设置
                    </button>
                </div>

                <div class="settings-layout">
                    <div class="settings-nav">
                        <div class="settings-nav-item active" data-section="general">基本设置</div>
                        <div class="settings-nav-item" data-section="security">安全设置</div>
                        <div class="settings-nav-item" data-section="email">邮件设置</div>
                        <div class="settings-nav-item" data-section="backup">备份设置</div>
                        <div class="settings-nav-item" data-section="advanced">高级设置</div>
                    </div>

                    <div class="settings-content">
                        <!-- 基本设置 -->
                        <div class="settings-section active" id="general">
                            <h3>基本设置</h3>
                            <div class="form-group">
                                <label>网站名称</label>
                                <input type="text" value="比特熊智慧系统" class="form-input">
                            </div>
                            <div class="form-group">
                                <label>网站描述</label>
                                <textarea class="form-input" rows="3">专业的在线学习平台</textarea>
                            </div>
                            <div class="form-group">
                                <label>管理员邮箱</label>
                                <input type="email" value="<EMAIL>" class="form-input">
                            </div>
                            <div class="form-group">
                                <label>时区设置</label>
                                <select class="form-input">
                                    <option value="Asia/Shanghai" selected>Asia/Shanghai (UTC+8)</option>
                                    <option value="UTC">UTC (UTC+0)</option>
                                    <option value="America/New_York">America/New_York (UTC-5)</option>
                                </select>
                            </div>
                        </div>

                        <!-- 安全设置 -->
                        <div class="settings-section" id="security">
                            <h3>安全设置</h3>
                            <div class="form-group">
                                <label>最大登录尝试次数</label>
                                <input type="number" value="5" class="form-input">
                            </div>
                            <div class="form-group">
                                <label>会话超时时间（分钟）</label>
                                <input type="number" value="60" class="form-input">
                            </div>
                            <div class="form-group">
                                <label>启用两步验证</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="twoFactor">
                                    <label for="twoFactor"></label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>强制HTTPS</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="forceHttps" checked>
                                    <label for="forceHttps"></label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 日程管理模态框 -->
    <div class="modal-overlay" id="eventModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">日程管理</h3>
                <button class="modal-close" onclick="closeEventModal()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <!-- 添加/编辑日程表单 -->
                <div id="eventForm" style="display: none;">
                    <form onsubmit="saveEvent(event)">
                        <div class="form-group">
                            <label class="form-label">日程标题</label>
                            <input type="text" class="form-input" id="eventTitle" placeholder="请输入日程标题" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">开始时间</label>
                            <input type="time" class="form-input" id="eventStartTime" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">结束时间</label>
                            <input type="time" class="form-input" id="eventEndTime" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">日程描述</label>
                            <textarea class="form-input form-textarea" id="eventDescription" placeholder="请输入日程描述（可选）"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" onclick="showEventList()">取消</button>
                            <button type="submit" class="btn btn-primary">保存</button>
                        </div>
                    </form>
                </div>

                <!-- 日程列表 -->
                <div id="eventList">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <span id="selectedDate" style="font-weight: 600; color: #1e293b;"></span>
                        <button class="btn btn-primary btn-small" onclick="showEventForm()">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" style="margin-right: 0.25rem;">
                                <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            添加日程
                        </button>
                    </div>
                    <div class="event-list" id="eventListContainer">
                        <!-- 日程项目将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 侧边栏切换功能
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const mobileOverlay = document.getElementById('mobileOverlay');

        sidebarToggle.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('open');
                mobileOverlay.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
            }
        });

        // 移动端遮罩层点击关闭
        mobileOverlay.addEventListener('click', function() {
            sidebar.classList.remove('open');
            mobileOverlay.classList.remove('show');
        });

        // 导航链接切换
        const navLinks = document.querySelectorAll('.nav-link');
        const pageTitle = document.getElementById('pageTitle');
        const dashboardContent = document.getElementById('dashboardContent');
        const pageDesignerContent = document.getElementById('pageDesignerContent');

        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // 移除所有活动状态
                navLinks.forEach(l => l.classList.remove('active'));
                // 添加当前活动状态
                this.classList.add('active');

                const page = this.dataset.page;

                // 切换页面内容
                if (page === 'dashboard') {
                    pageTitle.textContent = '仪表板';
                    dashboardContent.style.display = 'block';
                    pageDesignerContent.style.display = 'none';
                } else if (page === 'page-designer') {
                    pageTitle.textContent = '页面设计器';
                    dashboardContent.style.display = 'none';
                    pageDesignerContent.style.display = 'block';
                }

                // 移动端自动关闭侧边栏
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('open');
                    mobileOverlay.classList.remove('show');
                }
            });
        });

        // 通知功能
        function toggleNotifications() {
            const dropdown = document.getElementById('notificationDropdown');
            dropdown.classList.toggle('show');

            // 点击外部关闭通知
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.notification-btn')) {
                    dropdown.classList.remove('show');
                }
            });
        }

        // 一键已读功能
        async function markAllAsRead() {
            try {
                const response = await fetch('api/notifications.php', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action: 'mark_all_read' })
                });

                const result = await response.json();

                if (result.success) {
                    // 更新UI
                    const notificationItems = document.querySelectorAll('.notification-item.unread');
                    notificationItems.forEach(item => {
                        item.classList.remove('unread');
                    });

                    // 更新通知徽章
                    const badge = document.getElementById('notificationBadge');
                    badge.textContent = '0';
                    badge.style.display = 'none';

                    alert(result.message || '所有通知已标记为已读');
                } else {
                    alert('操作失败: ' + result.error);
                }
            } catch (error) {
                console.error('标记已读失败:', error);
                alert('操作失败，请重试');
            }
        }

        // 加载通知数据
        async function loadNotifications() {
            try {
                const response = await fetch('api/notifications.php');
                const result = await response.json();

                if (result.success) {
                    const notificationList = document.getElementById('notificationList');
                    notificationList.innerHTML = result.data.map(notification => `
                        <div class="notification-item ${notification.is_read ? '' : 'unread'}" data-id="${notification.id}">
                            <div class="notification-title">${notification.title}</div>
                            <div class="notification-text">${notification.content}</div>
                            <div class="notification-time">${notification.time_ago}</div>
                        </div>
                    `).join('');

                    // 更新未读数量
                    const unreadCount = result.data.filter(n => !n.is_read).length;
                    const badge = document.getElementById('notificationBadge');
                    if (unreadCount > 0) {
                        badge.textContent = unreadCount;
                        badge.style.display = 'flex';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('加载通知失败，使用模拟数据:', error);
                // 通知已经在HTML中，无需额外处理
            }
        }

        // 加载用户活动数据
        async function loadUserActivities() {
            try {
                const response = await fetch('api/activities.php');
                const result = await response.json();

                if (result.success) {
                    const activityFeed = document.querySelector('.activity-feed');
                    activityFeed.innerHTML = result.data.map(activity => {
                        const iconClass = getActivityIconClass(activity.activity_type);
                        return `
                            <div class="activity-item">
                                <div class="activity-icon ${iconClass}">
                                    ${getActivityIcon(activity.activity_type)}
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">${activity.title}</div>
                                    <div class="activity-description">${activity.description}</div>
                                    <div class="activity-time">${activity.time_ago}</div>
                                </div>
                            </div>
                        `;
                    }).join('');
                }
            } catch (error) {
                console.error('加载用户活动失败，使用模拟数据:', error);
                // 活动数据已经在HTML中，无需额外处理
            }
        }

        // 获取活动图标类名
        function getActivityIconClass(type) {
            switch (type) {
                case 'user_register':
                case 'user_login':
                    return 'user';
                case 'admin_login':
                case 'application_process':
                    return 'admin';
                case 'system_update':
                    return 'system';
                default:
                    return 'user';
            }
        }

        // 获取活动图标
        function getActivityIcon(type) {
            switch (type) {
                case 'user_register':
                case 'user_login':
                    return '<svg width="16" height="16" viewBox="0 0 24 24" fill="none"><path d="M20 21V19A4 4 0 0 0 16 15H8A4 4 0 0 0 4 19V21" stroke="currentColor" stroke-width="2"/><circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/></svg>';
                case 'admin_login':
                    return '<svg width="16" height="16" viewBox="0 0 24 24" fill="none"><path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2"/><path d="M21 12C21 16.97 16.97 21 12 21S3 16.97 3 12S7.03 3 12 3S21 7.03 21 12Z" stroke="currentColor" stroke-width="2"/></svg>';
                case 'application_process':
                    return '<svg width="16" height="16" viewBox="0 0 24 24" fill="none"><path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/><path d="M14 2V8H20" stroke="currentColor" stroke-width="2"/></svg>';
                case 'system_update':
                    return '<svg width="16" height="16" viewBox="0 0 24 24" fill="none"><path d="M12 22S8 18 8 14V5L12 3L16 5V14C16 18 12 22 12 22Z" stroke="currentColor" stroke-width="2"/></svg>';
                default:
                    return '<svg width="16" height="16" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/></svg>';
            }
        }

        // 加载统计数据
        async function loadStats() {
            try {
                const response = await fetch('api/stats.php');
                const result = await response.json();

                if (result.success) {
                    const data = result.data;

                    // 更新用户统计
                    if (data.users && data.users.roles) {
                        const statsGrid = document.querySelector('.stats-grid');
                        if (statsGrid) {
                            statsGrid.innerHTML = data.users.roles.map(role => `
                                <div class="stat-item">
                                    <span class="stat-label">${role.role_name}</span>
                                    <span class="stat-value">${role.count}</span>
                                </div>
                            `).join('');
                        }
                    }
                }
            } catch (error) {
                console.error('加载统计数据失败，使用模拟数据:', error);
                // 统计数据已经在HTML中，无需额外处理
            }
        }

        // 日历功能
        let currentDate = new Date();
        let currentMonth = currentDate.getMonth();
        let currentYear = currentDate.getFullYear();
        let selectedDateForModal = null;
        let editingEventId = null;

        // 日程数据存储
        let events = {};

        // 从API加载日程数据
        async function loadEvents() {
            try {
                const response = await fetch('api/events.php');
                const result = await response.json();

                if (result.success) {
                    // 重新组织数据结构
                    events = {};
                    result.data.forEach(event => {
                        const dateKey = formatDateKey(event.event_date);
                        if (!events[dateKey]) {
                            events[dateKey] = [];
                        }
                        events[dateKey].push({
                            id: event.id,
                            title: event.title,
                            startTime: event.start_time.substring(0, 5), // HH:MM格式
                            endTime: event.end_time.substring(0, 5),
                            description: event.description || ''
                        });
                    });

                    // 重新生成日历
                    generateCalendar(currentMonth, currentYear);
                }
            } catch (error) {
                console.error('加载日程失败，使用本地数据:', error);
                // 使用本地模拟数据
                loadMockEvents();
            }
        }

        // 加载模拟日程数据
        function loadMockEvents() {
            events = {
                '2025-1-15': [
                    {
                        id: 1,
                        title: '团队会议',
                        startTime: '09:00',
                        endTime: '10:30',
                        description: '讨论项目进度和下一步计划'
                    }
                ],
                '2025-1-20': [
                    {
                        id: 2,
                        title: '项目评审',
                        startTime: '14:00',
                        endTime: '16:00',
                        description: '对当前项目进行全面评审'
                    }
                ],
                '2025-1-25': [
                    {
                        id: 3,
                        title: '系统维护',
                        startTime: '02:00',
                        endTime: '04:00',
                        description: '定期系统维护和更新'
                    }
                ]
            };
            generateCalendar(currentMonth, currentYear);
        }

        // 格式化日期键
        function formatDateKey(dateStr) {
            const date = new Date(dateStr);
            return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
        }

        function generateCalendar(month, year) {
            const firstDay = new Date(year, month, 1).getDay();
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            const today = new Date();

            const calendarGrid = document.getElementById('calendarGrid');
            const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月',
                              '7月', '8月', '9月', '10月', '11月', '12月'];

            // 更新月份显示
            document.getElementById('calendarMonth').textContent = `${year}年${monthNames[month]}`;

            // 清除现有日期（保留头部）
            const headerDays = calendarGrid.querySelectorAll('.calendar-header-day');
            calendarGrid.innerHTML = '';
            headerDays.forEach(day => calendarGrid.appendChild(day));

            // 添加空白日期
            for (let i = 0; i < firstDay; i++) {
                const emptyDay = document.createElement('div');
                emptyDay.className = 'calendar-day';
                calendarGrid.appendChild(emptyDay);
            }

            // 添加日期
            for (let day = 1; day <= daysInMonth; day++) {
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day';
                dayElement.textContent = day;

                // 检查是否是今天
                if (year === today.getFullYear() &&
                    month === today.getMonth() &&
                    day === today.getDate()) {
                    dayElement.classList.add('today');
                }

                // 检查是否有事件
                const eventKey = `${year}-${month + 1}-${day}`;
                if (events[eventKey] && events[eventKey].length > 0) {
                    dayElement.classList.add('has-event');
                    dayElement.title = `${events[eventKey].length}个日程`;
                }

                // 添加点击事件
                dayElement.addEventListener('click', function() {
                    selectedDateForModal = eventKey;
                    showEventModal(year, month + 1, day);
                });

                calendarGrid.appendChild(dayElement);
            }
        }

        function previousMonth() {
            currentMonth--;
            if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            }
            generateCalendar(currentMonth, currentYear);
        }

        function nextMonth() {
            currentMonth++;
            if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            }
            generateCalendar(currentMonth, currentYear);
        }

        // 模态框功能
        function showEventModal(year, month, day) {
            const modal = document.getElementById('eventModal');
            const selectedDate = document.getElementById('selectedDate');
            selectedDate.textContent = `${year}年${month}月${day}日`;

            modal.classList.add('show');
            showEventList();
            renderEventList();
        }

        function closeEventModal() {
            const modal = document.getElementById('eventModal');
            modal.classList.remove('show');
            editingEventId = null;
        }

        function showEventForm(eventId = null) {
            const eventForm = document.getElementById('eventForm');
            const eventList = document.getElementById('eventList');

            eventForm.style.display = 'block';
            eventList.style.display = 'none';

            if (eventId) {
                // 编辑模式
                editingEventId = eventId;
                const eventData = findEventById(eventId);
                if (eventData) {
                    document.getElementById('eventTitle').value = eventData.title;
                    document.getElementById('eventStartTime').value = eventData.startTime;
                    document.getElementById('eventEndTime').value = eventData.endTime;
                    document.getElementById('eventDescription').value = eventData.description || '';
                }
            } else {
                // 新增模式
                editingEventId = null;
                document.getElementById('eventTitle').value = '';
                document.getElementById('eventStartTime').value = '';
                document.getElementById('eventEndTime').value = '';
                document.getElementById('eventDescription').value = '';
            }
        }

        function showEventList() {
            const eventForm = document.getElementById('eventForm');
            const eventList = document.getElementById('eventList');

            eventForm.style.display = 'none';
            eventList.style.display = 'block';
        }

        async function saveEvent(e) {
            e.preventDefault();

            const title = document.getElementById('eventTitle').value;
            const startTime = document.getElementById('eventStartTime').value;
            const endTime = document.getElementById('eventEndTime').value;
            const description = document.getElementById('eventDescription').value;

            if (!selectedDateForModal) return;

            try {
                const eventDate = selectedDateForModal.split('-');
                const formattedDate = `${eventDate[0]}-${eventDate[1].padStart(2, '0')}-${eventDate[2].padStart(2, '0')}`;

                const eventData = {
                    title,
                    description,
                    event_date: formattedDate,
                    start_time: startTime,
                    end_time: endTime
                };

                let response;
                if (editingEventId) {
                    // 编辑现有事件
                    eventData.id = editingEventId;
                    response = await fetch('api/events.php', {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(eventData)
                    });
                } else {
                    // 添加新事件
                    response = await fetch('api/events.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(eventData)
                    });
                }

                const result = await response.json();

                if (result.success) {
                    // 重新加载日程数据
                    await loadEvents();

                    // 返回列表视图
                    showEventList();
                    renderEventList();

                    alert(result.message || '日程保存成功');
                } else {
                    alert('保存失败: ' + result.error);
                }
            } catch (error) {
                console.error('API保存失败，使用本地存储:', error);
                // 使用本地存储作为回退
                saveEventLocally(title, startTime, endTime, description);
            }
        }

        // 本地保存日程
        function saveEventLocally(title, startTime, endTime, description) {
            if (!events[selectedDateForModal]) {
                events[selectedDateForModal] = [];
            }

            if (editingEventId) {
                // 编辑现有事件
                const eventIndex = events[selectedDateForModal].findIndex(e => e.id === editingEventId);
                if (eventIndex !== -1) {
                    events[selectedDateForModal][eventIndex] = {
                        id: editingEventId,
                        title,
                        startTime,
                        endTime,
                        description
                    };
                }
            } else {
                // 添加新事件
                const newEvent = {
                    id: Date.now(),
                    title,
                    startTime,
                    endTime,
                    description
                };
                events[selectedDateForModal].push(newEvent);
            }

            // 更新日历显示
            generateCalendar(currentMonth, currentYear);

            // 返回列表视图
            showEventList();
            renderEventList();

            alert('日程保存成功（本地存储）');
        }

        async function deleteEvent(eventId) {
            if (!confirm('确定要删除这个日程吗？')) return;

            try {
                const response = await fetch(`api/events.php?id=${eventId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    // 重新加载日程数据
                    await loadEvents();
                    renderEventList();
                    alert(result.message || '日程删除成功');
                } else {
                    alert('删除失败: ' + result.error);
                }
            } catch (error) {
                console.error('API删除失败，使用本地删除:', error);
                // 使用本地删除作为回退
                deleteEventLocally(eventId);
            }
        }

        // 本地删除日程
        function deleteEventLocally(eventId) {
            if (events[selectedDateForModal]) {
                events[selectedDateForModal] = events[selectedDateForModal].filter(e => e.id !== eventId);
                if (events[selectedDateForModal].length === 0) {
                    delete events[selectedDateForModal];
                }
            }

            generateCalendar(currentMonth, currentYear);
            renderEventList();
            alert('日程删除成功（本地存储）');
        }

        function findEventById(eventId) {
            if (!events[selectedDateForModal]) return null;
            return events[selectedDateForModal].find(e => e.id === eventId);
        }

        function renderEventList() {
            const container = document.getElementById('eventListContainer');

            if (!events[selectedDateForModal] || events[selectedDateForModal].length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                            <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
                            <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
                            <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        <p>这一天还没有安排日程</p>
                    </div>
                `;
                return;
            }

            const eventsList = events[selectedDateForModal].sort((a, b) => a.startTime.localeCompare(b.startTime));

            container.innerHTML = eventsList.map(event => `
                <div class="event-item">
                    <div class="event-title">${event.title}</div>
                    <div class="event-time">${event.startTime} - ${event.endTime}</div>
                    ${event.description ? `<div class="event-description">${event.description}</div>` : ''}
                    <div class="event-actions">
                        <button class="btn btn-secondary btn-small" onclick="showEventForm(${event.id})">编辑</button>
                        <button class="btn btn-danger btn-small" onclick="deleteEvent(${event.id})">删除</button>
                    </div>
                </div>
            `).join('');
        }

        // 用户菜单
        function showUserMenu() {
            const options = ['个人设置', '修改密码', '退出登录'];
            const choice = prompt('请选择操作：\n1. 个人设置\n2. 修改密码\n3. 退出登录');

            if (choice === '3') {
                if (confirm('确定要退出登录吗？')) {
                    window.location.href = 'admin-login.php';
                }
            }
        }

        // 用户菜单功能
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }

        // 确认退出登录
        function confirmLogout() {
            return confirm('确定要退出登录吗？');
        }

        // 显示个人资料（占位函数）
        function showProfile() {
            alert('个人资料功能开发中...');
        }

        // 显示系统设置（占位函数）
        function showSettings() {
            alert('系统设置功能开发中...');
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('eventModal');
            if (e.target === modal) {
                closeEventModal();
            }

            // 点击外部关闭用户菜单
            const userDropdown = document.getElementById('userDropdown');
            const userAvatar = document.querySelector('.user-avatar');
            if (!userAvatar.contains(e.target)) {
                userDropdown.classList.remove('show');
            }
        });

        // 响应式处理
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                sidebar.classList.remove('open');
                mobileOverlay.classList.remove('show');
            }
        });

        // 页面切换功能
        function switchPage(pageId) {
            // 隐藏所有页面内容
            const allPages = document.querySelectorAll('.page-content, .content-area');
            allPages.forEach(page => {
                page.style.display = 'none';
            });

            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.style.display = 'block';
            } else {
                // 如果是仪表板，显示默认内容区域
                if (pageId === 'dashboard') {
                    document.getElementById('contentArea').style.display = 'block';
                }
            }

            // 更新导航状态
            const allNavLinks = document.querySelectorAll('.nav-link');
            allNavLinks.forEach(link => {
                link.classList.remove('active');
            });

            const activeLink = document.querySelector(`[data-page="${pageId}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }

            // 更新页面标题
            const pageTitles = {
                'dashboard': '仪表板',
                'user-management': '用户管理',
                'page-list': '页面列表',
                'page-management': '主页设计器',
                'navbar-management': '导航栏管理',
                'app-management': '应用管理',
                'announcement': '公告栏',
                'mailbox': '站内邮箱',
                'system-settings': '系统设置'
            };

            // 特殊处理：如果是导航栏管理页面，加载导航栏数据
            if (pageId === 'navbar-management') {
                setTimeout(() => {
                    if (typeof loadNavbarData === 'function') {
                        loadNavbarData();
                    }
                }, 100);
            }

            // 如果是从快捷入口访问的模块，添加返回按钮
            if (['community-management', 'announcement', 'mailbox'].includes(pageId)) {
                addBackToDashboard();
            }

            const pageTitle = document.getElementById('pageTitle');
            if (pageTitle && pageTitles[pageId]) {
                pageTitle.textContent = pageTitles[pageId];
            }
        }

        // 导航点击事件
        document.addEventListener('click', function(e) {
            const navLink = e.target.closest('.nav-link');
            const submenuLink = e.target.closest('.submenu-link');

            if (submenuLink) {
                e.preventDefault();
                const pageId = submenuLink.getAttribute('data-page');
                if (pageId) {
                    // 更新子菜单活跃状态
                    document.querySelectorAll('.submenu-link').forEach(link => {
                        link.classList.remove('active');
                    });
                    submenuLink.classList.add('active');

                    // 更新父菜单活跃状态
                    const parentNavItem = submenuLink.closest('.nav-item');
                    const parentNavLink = parentNavItem.querySelector('.nav-link');
                    document.querySelectorAll('.nav-link').forEach(link => {
                        link.classList.remove('active');
                    });
                    parentNavLink.classList.add('active');

                    switchPage(pageId);
                }
            } else if (navLink) {
                e.preventDefault();
                const navItem = navLink.closest('.nav-item');

                // 处理有子菜单的项目
                if (navItem.classList.contains('has-submenu')) {
                    navItem.classList.toggle('expanded');
                    return;
                }

                const pageId = navLink.getAttribute('data-page');
                if (pageId) {
                    switchPage(pageId);
                }
            }
        });

        // 管理功能的占位函数
        function showAddUserModal() {
            alert('添加用户功能开发中...');
        }

        function editUser(id) {
            alert(`编辑用户 ${id} 功能开发中...`);
        }

        function deleteUser(id) {
            if (confirm(`确定要删除用户 ${id} 吗？`)) {
                alert('删除功能开发中...');
            }
        }

        function showAddPageModal() {
            alert('新建页面功能开发中...');
        }

        function editPage(id) {
            alert(`编辑页面 ${id} 功能开发中...`);
        }

        function previewPage(id) {
            alert(`预览页面 ${id} 功能开发中...`);
        }

        function deletePage(id) {
            if (confirm(`确定要删除页面 ${id} 吗？`)) {
                alert('删除功能开发中...');
            }
        }

        // 社区管理功能
        function showCommunityStats() {
            switchPage('community-stats');
        }

        function showAddPostModal() {
            showModal('addPostModal');
        }

        // 帖子管理功能
        function viewPost(id) {
            // 在新窗口打开帖子详情页
            window.open(`community-post-detail.php?id=${id}`, '_blank');
        }

        function editPost(id) {
            // 跳转到编辑页面
            window.open(`community-post.php?edit=${id}`, '_blank');
        }

        function deletePost(id) {
            if (confirm(`确定要删除帖子 ${id} 吗？此操作不可恢复！`)) {
                fetch(`api/admin-community.php`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action: 'delete_post', id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('帖子删除成功');
                        location.reload(); // 刷新页面
                    } else {
                        alert('删除失败: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('删除失败: ' + error.message);
                });
            }
        }

        function hidePost(id) {
            if (confirm(`确定要隐藏帖子 ${id} 吗？`)) {
                updatePostStatus(id, 'hidden', '隐藏');
            }
        }

        function publishPost(id) {
            if (confirm(`确定要发布帖子 ${id} 吗？`)) {
                updatePostStatus(id, 'published', '发布');
            }
        }

        function pinPost(id) {
            if (confirm(`确定要置顶帖子 ${id} 吗？`)) {
                updatePostPin(id, true, '置顶');
            }
        }

        function unpinPost(id) {
            if (confirm(`确定要取消置顶帖子 ${id} 吗？`)) {
                updatePostPin(id, false, '取消置顶');
            }
        }

        function updatePostStatus(id, status, action) {
            fetch(`api/admin-community.php`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ action: 'update_post_status', id: id, status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`帖子${action}成功`);
                    location.reload();
                } else {
                    alert(`${action}失败: ` + data.error);
                }
            })
            .catch(error => {
                alert(`${action}失败: ` + error.message);
            });
        }

        function updatePostPin(id, isPinned, action) {
            fetch(`api/admin-community.php`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ action: 'pin_post', id: id, is_pinned: isPinned ? 'true' : 'false' })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`帖子${action}成功`);
                    location.reload();
                } else {
                    alert(`${action}失败: ` + data.error);
                }
            })
            .catch(error => {
                alert(`${action}失败: ` + error.message);
            });
        }

        function bulkPublish() {
            const selectedPosts = document.querySelectorAll('.post-checkbox:checked');
            if (selectedPosts.length === 0) {
                alert('请选择要操作的帖子');
                return;
            }

            const postIds = Array.from(selectedPosts).map(cb => cb.getAttribute('data-id'));

            if (confirm(`确定要批量发布 ${selectedPosts.length} 个帖子吗？`)) {
                fetch(`api/admin-community.php`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action: 'bulk_update_posts', ids: postIds, status: 'published' })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`已批量发布 ${selectedPosts.length} 个帖子`);
                        location.reload();
                    } else {
                        alert('批量操作失败: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('批量操作失败: ' + error.message);
                });
            }
        }

        function bulkDelete() {
            const selectedPosts = document.querySelectorAll('.post-checkbox:checked');
            if (selectedPosts.length === 0) {
                alert('请选择要操作的帖子');
                return;
            }

            const postIds = Array.from(selectedPosts).map(cb => cb.getAttribute('data-id'));

            if (confirm(`确定要批量删除 ${selectedPosts.length} 个帖子吗？此操作不可恢复！`)) {
                fetch(`api/admin-community.php`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action: 'bulk_delete_posts', ids: postIds })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`已批量删除 ${selectedPosts.length} 个帖子`);
                        location.reload();
                    } else {
                        alert('批量删除失败: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('批量删除失败: ' + error.message);
                });
            }
        }

        function bulkHide() {
            const selectedPosts = document.querySelectorAll('.post-checkbox:checked');
            if (selectedPosts.length === 0) {
                alert('请选择要操作的帖子');
                return;
            }

            const postIds = Array.from(selectedPosts).map(cb => cb.getAttribute('data-id'));

            if (confirm(`确定要批量隐藏 ${selectedPosts.length} 个帖子吗？`)) {
                fetch(`api/admin-community.php`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action: 'bulk_update_posts', ids: postIds, status: 'hidden' })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`已批量隐藏 ${selectedPosts.length} 个帖子`);
                        location.reload();
                    } else {
                        alert('批量隐藏失败: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('批量隐藏失败: ' + error.message);
                });
            }
        }

        function exportPosts() {
            window.open('api/admin-community.php?action=export_posts', '_blank');
        }

        // 评论管理功能
        function viewCommentDetail(id) {
            fetch(`api/admin-community.php?action=get_comment&id=${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const comment = data.comment;
                        const modalContent = `
                            <div class="comment-detail-modal">
                                <h3>评论详情</h3>
                                <div class="detail-section">
                                    <strong>作者：</strong>${comment.username}<br>
                                    <strong>发布时间：</strong>${comment.created_at}<br>
                                    <strong>状态：</strong>${comment.status}<br>
                                    <strong>所属帖子：</strong>${comment.post_title}
                                </div>
                                <div class="detail-section">
                                    <strong>评论内容：</strong><br>
                                    <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; margin-top: 0.5rem;">
                                        ${comment.content}
                                    </div>
                                </div>
                            </div>
                        `;
                        showModal('评论详情', modalContent);
                    } else {
                        alert('获取评论详情失败：' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取评论详情失败');
                });
        }

        function editComment(id) {
            // 获取评论内容并显示编辑框
            fetch(`api/admin-community.php?action=get_comment&id=${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const comment = data.comment;
                        const newContent = prompt('编辑评论内容：', comment.content);
                        if (newContent !== null && newContent.trim() !== '') {
                            updateComment(id, newContent);
                        }
                    } else {
                        alert('获取评论内容失败：' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取评论内容失败');
                });
        }

        function updateComment(id, content) {
            fetch('api/admin-community.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=update_comment&id=${id}&content=${encodeURIComponent(content)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('评论更新成功');
                    location.reload();
                } else {
                    alert('更新失败：' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('更新失败');
            });
        }

        function deleteComment(id) {
            if (confirm(`确定要删除这条评论吗？`)) {
                fetch('api/admin-community.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=delete_comment&id=${id}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('评论删除成功');
                        location.reload();
                    } else {
                        alert('删除失败：' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败');
                });
            }
        }

        function approveComment(id) {
            if (confirm(`确定要通过这条评论吗？`)) {
                updateCommentStatus(id, 'approved');
            }
        }

        function rejectComment(id) {
            if (confirm(`确定要拒绝这条评论吗？`)) {
                updateCommentStatus(id, 'spam');
            }
        }

        function hideComment(id) {
            if (confirm(`确定要隐藏这条评论吗？`)) {
                updateCommentStatus(id, 'hidden');
            }
        }

        function markAsSpam(id) {
            if (confirm(`确定要将这条评论标记为垃圾评论吗？`)) {
                updateCommentStatus(id, 'spam');
            }
        }

        function updateCommentStatus(id, status) {
            fetch('api/admin-community.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=update_comment_status&id=${id}&status=${status}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('评论状态更新成功');
                    location.reload();
                } else {
                    alert('更新失败：' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('更新失败');
            });
        }

        function dismissReport(id) {
            if (confirm('确定要驳回对这条评论的举报吗？')) {
                fetch('api/admin-community.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=dismiss_comment_reports&comment_id=${id}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('举报已驳回');
                        location.reload();
                    } else {
                        alert('操作失败：' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败');
                });
            }
        }

        function bulkApproveComments() {
            alert('批量通过评论功能开发中...');
        }

        function bulkDeleteComments() {
            alert('批量删除评论功能开发中...');
        }

        function exportComments() {
            alert('正在导出评论数据...');
        }

        function clearSpamComments() {
            if (confirm('确定要清理所有垃圾评论吗？')) {
                alert('垃圾评论清理完成');
            }
        }

        // 分类管理功能
        function showAddCategoryModal() {
            showModal('addCategoryModal');
        }

        function editCategory(id) {
            alert(`编辑分类 ${id}`);
        }

        function deleteCategory(id) {
            if (confirm(`确定要删除分类 ${id} 吗？`)) {
                alert(`分类 ${id} 已删除`);
            }
        }

        function enableCategory(id) {
            if (confirm(`确定要启用分类 ${id} 吗？`)) {
                alert(`分类 ${id} 已启用`);
            }
        }

        function sortCategories() {
            alert('分类排序功能开发中...');
        }

        function reorderCategories() {
            alert('重新排序功能开发中...');
        }

        function importCategories() {
            alert('导入分类功能开发中...');
        }

        // 统计功能
        function exportStats() {
            alert('正在导出统计报告...');
        }

        function refreshStats() {
            alert('正在刷新统计数据...');
        }

        // 举报管理功能
        function acceptReport(id) {
            if (confirm(`确定要确认举报 ${id} 为违规吗？`)) {
                alert(`举报 ${id} 已确认为违规，相关内容已处理`);
            }
        }

        function dismissReport(id) {
            if (confirm(`确定要驳回举报 ${id} 吗？`)) {
                alert(`举报 ${id} 已驳回`);
            }
        }

        function viewReportDetail(id) {
            alert(`查看举报 ${id} 的详细信息`);
        }

        function contactUser(id) {
            alert(`联系相关用户处理举报 ${id}`);
        }

        function hideComment(id) {
            if (confirm(`确定要隐藏评论 ${id} 吗？`)) {
                alert(`评论 ${id} 已隐藏`);
            }
        }

        function bulkAcceptReports() {
            alert('批量确认举报功能开发中...');
        }

        function bulkDismissReports() {
            alert('批量驳回举报功能开发中...');
        }

        function exportReports() {
            alert('正在导出举报数据...');
        }

        function clearProcessedReports() {
            if (confirm('确定要清理所有已处理的举报记录吗？')) {
                alert('已处理举报记录清理完成');
            }
        }

        // 模态框管理函数
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'flex';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 添加帖子功能
        function submitAddPost() {
            const title = document.getElementById('postTitle').value;
            const category = document.getElementById('postCategory').value;
            const content = document.getElementById('postContent').value;
            const tags = document.getElementById('postTags').value;
            const visible = document.getElementById('postVisible').checked;

            if (!title || !category || !content) {
                alert('请填写所有必填字段');
                return;
            }

            const postData = {
                title: title,
                category_id: category,
                content: content,
                tags: tags,
                status: visible ? 'published' : 'draft'
            };

            fetch('api/admin-community.php?action=create_post', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(postData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`帖子 "${title}" 已${visible ? '发布' : '保存为草稿'}成功`);
                    closeModal('addPostModal');
                    document.getElementById('addPostForm').reset();
                    location.reload();
                } else {
                    alert('发布失败: ' + data.error);
                }
            })
            .catch(error => {
                alert('发布失败: ' + error.message);
            });
        }

        // 添加分类功能
        function submitAddCategory() {
            const name = document.getElementById('categoryName').value;
            const description = document.getElementById('categoryDescription').value;
            const color = document.getElementById('categoryColor').value;
            const order = document.getElementById('categoryOrder').value;
            const enabled = document.getElementById('categoryEnabled').checked;

            if (!name) {
                alert('请输入分类名称');
                return;
            }

            alert(`分类 "${name}" 已添加${enabled ? '并启用' : '但未启用'}`);
            closeModal('addCategoryModal');

            // 清空表单
            document.getElementById('addCategoryForm').reset();
        }

        // 复选框管理功能
        document.addEventListener('DOMContentLoaded', function() {
            // 全选帖子功能
            const selectAllPosts = document.getElementById('selectAllPosts');
            if (selectAllPosts) {
                selectAllPosts.addEventListener('change', function() {
                    const checkboxes = document.querySelectorAll('.post-checkbox');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateBulkActionButtons();
                });
            }

            // 监听单个复选框变化
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('post-checkbox')) {
                    updateBulkActionButtons();

                    // 更新全选状态
                    const allCheckboxes = document.querySelectorAll('.post-checkbox');
                    const checkedCheckboxes = document.querySelectorAll('.post-checkbox:checked');
                    const selectAll = document.getElementById('selectAllPosts');

                    if (selectAll) {
                        selectAll.checked = allCheckboxes.length === checkedCheckboxes.length;
                        selectAll.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
                    }
                }
            });

            // 筛选标签功能
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('filter-tab')) {
                    // 移除其他标签的active状态
                    const tabs = e.target.parentElement.querySelectorAll('.filter-tab');
                    tabs.forEach(tab => tab.classList.remove('active'));

                    // 添加当前标签的active状态
                    e.target.classList.add('active');

                    // 执行筛选逻辑
                    const filter = e.target.getAttribute('data-filter');
                    filterContent(filter);
                }
            });

            // 搜索功能
            const searchInputs = ['postSearchInput', 'commentSearchInput', 'categorySearchInput', 'reportSearchInput'];
            searchInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('input', function() {
                        searchContent(this.value, inputId);
                    });
                }
            });
        });

        function updateBulkActionButtons() {
            const checkedBoxes = document.querySelectorAll('.post-checkbox:checked');
            const bulkPublishBtn = document.getElementById('bulkPublishBtn');
            const bulkHideBtn = document.getElementById('bulkHideBtn');
            const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');

            const hasSelection = checkedBoxes.length > 0;

            if (bulkPublishBtn) bulkPublishBtn.disabled = !hasSelection;
            if (bulkHideBtn) bulkHideBtn.disabled = !hasSelection;
            if (bulkDeleteBtn) bulkDeleteBtn.disabled = !hasSelection;
        }

        function filterContent(filter) {
            const rows = document.querySelectorAll('#postsTableBody tr[data-status]');

            rows.forEach(row => {
                const status = row.getAttribute('data-status');
                const isPinned = row.getAttribute('data-pinned') === '1';

                let shouldShow = false;

                switch(filter) {
                    case 'all':
                        shouldShow = true;
                        break;
                    case 'published':
                        shouldShow = status === 'published';
                        break;
                    case 'draft':
                        shouldShow = status === 'draft';
                        break;
                    case 'hidden':
                        shouldShow = status === 'hidden';
                        break;
                    case 'pinned':
                        shouldShow = isPinned;
                        break;
                    default:
                        shouldShow = true;
                }

                row.style.display = shouldShow ? '' : 'none';
            });
        }

        function searchContent(query, inputId) {
            if (inputId === 'postSearchInput') {
                const rows = document.querySelectorAll('#postsTableBody tr[data-status]');
                const searchTerm = query.toLowerCase();

                rows.forEach(row => {
                    const titleCell = row.querySelector('.post-title-cell');
                    if (titleCell) {
                        const title = titleCell.textContent.toLowerCase();
                        const shouldShow = title.includes(searchTerm);
                        row.style.display = shouldShow ? '' : 'none';
                    }
                });
            }
        }

        // 分页功能
        function previousPage() {
            alert('上一页功能开发中...');
        }

        function nextPage() {
            alert('下一页功能开发中...');
        }

        function previousCommentsPage() {
            alert('评论上一页功能开发中...');
        }

        function nextCommentsPage() {
            alert('评论下一页功能开发中...');
        }

        function previousReportsPage() {
            alert('举报上一页功能开发中...');
        }

        function nextReportsPage() {
            alert('举报下一页功能开发中...');
        }

        function showAddAnnouncementModal() {
            alert('发布公告功能开发中...');
        }

        function showComposeModal() {
            alert('写邮件功能开发中...');
        }

        function saveSettings() {
            alert('保存设置功能开发中...');
        }

        // 添加返回仪表板按钮
        function addBackToDashboard() {
            // 检查是否已经有返回按钮
            if (document.getElementById('backToDashboard')) {
                return;
            }

            // 创建返回按钮
            const backButton = document.createElement('button');
            backButton.id = 'backToDashboard';
            backButton.className = 'btn btn-secondary back-button';
            backButton.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2"/>
                </svg>
                返回仪表板
            `;
            backButton.onclick = () => {
                switchPage('dashboard');
                removeBackButton();
            };

            // 添加到页面头部
            const pageHeader = document.querySelector('.page-header');
            if (pageHeader) {
                pageHeader.insertBefore(backButton, pageHeader.firstChild);
            }
        }

        // 移除返回按钮
        function removeBackButton() {
            const backButton = document.getElementById('backToDashboard');
            if (backButton) {
                backButton.remove();
            }
        }

        // 更新快捷卡片统计数据
        async function updateShortcutStats() {
            try {
                // 更新公告统计
                const announcementResponse = await fetch('api/announcements.php');
                if (announcementResponse.ok) {
                    const announcementData = await announcementResponse.json();
                    if (announcementData.success) {
                        const announcements = announcementData.data.announcements;
                        const importantCount = announcements.filter(a => a.type === 'important').length;

                        const announcementCard = document.querySelector('.shortcut-card[onclick*="announcement"]');
                        if (announcementCard) {
                            const statsDiv = announcementCard.querySelector('.shortcut-stats');
                            statsDiv.innerHTML = `
                                <span class="stat-badge">${announcements.length} 条公告</span>
                                <span class="stat-badge important">${importantCount} 条重要</span>
                            `;
                        }
                    }
                }

                // 更新通知统计（用于邮箱）
                const notificationResponse = await fetch('api/notifications.php?action=count');
                if (notificationResponse.ok) {
                    const notificationData = await notificationResponse.json();
                    if (notificationData.success) {
                        const unreadCount = notificationData.data.count;

                        const mailboxCard = document.querySelector('.shortcut-card[onclick*="mailbox"]');
                        if (mailboxCard) {
                            const statsDiv = mailboxCard.querySelector('.shortcut-stats');
                            statsDiv.innerHTML = `
                                <span class="stat-badge">${unreadCount} 条未读</span>
                                <span class="stat-badge">12 条消息</span>
                            `;
                        }
                    }
                }
            } catch (error) {
                console.error('更新快捷卡片统计失败:', error);
            }
        }

        // 设置页面切换
        document.addEventListener('click', function(e) {
            const settingsNavItem = e.target.closest('.settings-nav-item');
            if (settingsNavItem) {
                // 移除所有活跃状态
                document.querySelectorAll('.settings-nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelectorAll('.settings-section').forEach(section => {
                    section.classList.remove('active');
                });

                // 添加活跃状态
                settingsNavItem.classList.add('active');
                const sectionId = settingsNavItem.getAttribute('data-section');
                const targetSection = document.getElementById(sectionId);
                if (targetSection) {
                    targetSection.classList.add('active');
                }
            }
        });

        // 邮箱文件夹切换
        document.addEventListener('click', function(e) {
            const mailboxNavItem = e.target.closest('.mailbox-nav .nav-item');
            if (mailboxNavItem) {
                document.querySelectorAll('.mailbox-nav .nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                mailboxNavItem.classList.add('active');

                const folder = mailboxNavItem.getAttribute('data-folder');
                // 这里可以添加加载不同文件夹邮件的逻辑
                console.log('切换到文件夹:', folder);
            }
        });



        // 导航栏管理功能
        let currentNavbarData = [];
        let selectedMenuItem = null;

        // 默认导航栏数据
        const defaultNavbarData = [
            {
                id: 1,
                name: '首页',
                url: '/index.php',
                type: 'link',
                visible: true,
                order: 1,
                icon: null
            },
            {
                id: 2,
                name: '组织',
                url: '#',
                type: 'dropdown',
                visible: true,
                order: 2,
                icon: null,
                children: [
                    {
                        id: 21,
                        name: '关于我们',
                        url: '/about.php',
                        type: 'submenu',
                        visible: true,
                        order: 1,
                        parent_id: 2
                    },
                    {
                        id: 22,
                        name: '团队介绍',
                        url: '/team.php',
                        type: 'submenu',
                        visible: true,
                        order: 2,
                        parent_id: 2
                    },
                    {
                        id: 23,
                        name: '联系我们',
                        url: '/contact.php',
                        type: 'submenu',
                        visible: true,
                        order: 3,
                        parent_id: 2
                    }
                ]
            },
            {
                id: 3,
                name: '服务',
                url: '/services.php',
                type: 'link',
                visible: true,
                order: 3,
                icon: null
            },
            {
                id: 4,
                name: '新闻',
                url: '/news.php',
                type: 'link',
                visible: true,
                order: 4,
                icon: null
            }
        ];

        // 初始化导航栏管理
        function initNavbarManagement() {
            currentNavbarData = [...defaultNavbarData];
            renderNavbarTree();
        }

        // 渲染导航栏树
        function renderNavbarTree() {
            const treeContainer = document.getElementById('navbarTree');
            if (!treeContainer) return;

            treeContainer.innerHTML = '';

            currentNavbarData.forEach(item => {
                const itemElement = createNavTreeItem(item);
                treeContainer.appendChild(itemElement);
            });
        }

        // 创建导航树项目
        function createNavTreeItem(item) {
            const div = document.createElement('div');
            div.className = 'nav-tree-item';
            div.dataset.itemId = item.id;

            div.innerHTML = `
                <div class="nav-item-header">
                    <div class="nav-item-drag">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <circle cx="9" cy="12" r="1" fill="currentColor"/>
                            <circle cx="9" cy="5" r="1" fill="currentColor"/>
                            <circle cx="9" cy="19" r="1" fill="currentColor"/>
                            <circle cx="15" cy="12" r="1" fill="currentColor"/>
                            <circle cx="15" cy="5" r="1" fill="currentColor"/>
                            <circle cx="15" cy="19" r="1" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="nav-item-icon">
                        ${item.type === 'dropdown' ?
                            '<svg width="20" height="20" viewBox="0 0 24 24" fill="none"><path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2"/></svg>' :
                            '<svg width="20" height="20" viewBox="0 0 24 24" fill="none"><path d="M10 13A5 5 0 0 0 7.54 7.54L4.93 4.93A10 10 0 1 1 19.07 19.07L16.46 16.46A5 5 0 0 0 10 13Z" stroke="currentColor" stroke-width="2"/></svg>'
                        }
                    </div>
                    <div class="nav-item-content">
                        <div class="nav-item-title">${item.name}</div>
                        <div class="nav-item-url">${item.url}</div>
                    </div>
                    <div class="nav-item-actions">
                        <button class="nav-item-btn" onclick="editNavItem(${item.id})" title="编辑">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M11 4H4A2 2 0 0 0 2 6V18A2 2 0 0 0 4 20H16A2 2 0 0 0 18 18V13" stroke="currentColor" stroke-width="2"/>
                                <path d="M18.5 2.5A2.121 2.121 0 0 1 21 5L12 14L8 15L9 11L18.5 2.5Z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                        <button class="nav-item-btn" onclick="deleteNavItem(${item.id})" title="删除">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                                <path d="M19 6V20A2 2 0 0 1 17 22H7A2 2 0 0 1 5 20V6" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                </div>
                ${item.children ? `<div class="nav-submenu">${item.children.map(child => createNavTreeItem(child).outerHTML).join('')}</div>` : ''}
            `;

            // 添加点击事件
            div.addEventListener('click', (e) => {
                if (!e.target.closest('.nav-item-btn')) {
                    selectNavItem(item.id);
                }
            });

            return div;
        }

        // 选择导航项目
        function selectNavItem(itemId) {
            // 移除之前的选中状态
            document.querySelectorAll('.nav-tree-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 添加选中状态
            const selectedElement = document.querySelector(`[data-item-id="${itemId}"]`);
            if (selectedElement) {
                selectedElement.classList.add('selected');
            }

            // 查找选中的菜单项
            selectedMenuItem = findNavItem(itemId);
            if (selectedMenuItem) {
                showMenuEditor(selectedMenuItem);
            }
        }

        // 查找导航项目
        function findNavItem(itemId) {
            for (const item of currentNavbarData) {
                if (item.id === itemId) {
                    return item;
                }
                if (item.children) {
                    for (const child of item.children) {
                        if (child.id === itemId) {
                            return child;
                        }
                    }
                }
            }
            return null;
        }

        // 显示菜单编辑器
        function showMenuEditor(item) {
            const noSelection = document.querySelector('.no-selection');
            const menuForm = document.getElementById('menuForm');

            if (noSelection) noSelection.style.display = 'none';
            if (menuForm) {
                menuForm.style.display = 'block';

                // 填充表单数据
                document.getElementById('menuName').value = item.name || '';
                document.getElementById('menuUrl').value = item.url || '';
                document.getElementById('menuIcon').value = item.icon || '';
                document.getElementById('menuType').value = item.type || 'link';
                document.getElementById('menuVisible').checked = item.visible !== false;
                document.getElementById('menuOrder').value = item.order || 0;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateCalendar(currentMonth, currentYear);

            // 加载数据
            loadEvents();
            loadNotifications();
            loadUserActivities();
            loadStats();
            updateShortcutStats();

            // 初始化导航栏管理
            initNavbarManagement();

            // 定期刷新数据
            setInterval(() => {
                loadNotifications();
                loadUserActivities();
                updateShortcutStats();
            }, 30000); // 每30秒刷新一次
        });

        // 导航栏管理功能函数
        function addNavItem() {
            // 填充父菜单选项
            populateParentMenuOptions();

            // 显示模态框
            document.getElementById('addNavItemModal').style.display = 'flex';
        }

        function closeAddNavItemModal() {
            document.getElementById('addNavItemModal').style.display = 'none';

            // 清空表单
            document.getElementById('addNavItemForm').reset();
            document.getElementById('addMenuVisible').checked = true;
            document.getElementById('addMenuOrder').value = 0;
            document.getElementById('addParentGroup').style.display = 'none';
        }

        function populateParentMenuOptions() {
            const parentSelect = document.getElementById('addParentMenu');
            parentSelect.innerHTML = '<option value="">选择父菜单</option>';

            // 只显示下拉菜单类型的项目作为父菜单选项
            currentNavbarData.forEach(item => {
                if (item.type === 'dropdown') {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = item.name;
                    parentSelect.appendChild(option);
                }
            });
        }

        function submitAddNavItem() {
            const form = document.getElementById('addNavItemForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const menuType = document.getElementById('addMenuType').value;
            const parentId = document.getElementById('addParentMenu').value;

            const newItem = {
                name: document.getElementById('addMenuName').value,
                url: document.getElementById('addMenuUrl').value,
                type: menuType === 'submenu' || parentId ? 'submenu' : menuType,
                parent_id: parentId || null,
                icon: document.getElementById('addMenuIcon').value || null,
                visible: document.getElementById('addMenuVisible').checked,
                sort_order: parseInt(document.getElementById('addMenuOrder').value) || 0
            };

            // 发送到API
            fetch('api/navbar.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(newItem)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('菜单项添加成功');
                    closeAddNavItemModal();
                    loadNavbarData();
                } else {
                    alert('添加失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('添加菜单项失败:', error);
                alert('添加失败，请重试');
            });
        }

        // 处理菜单类型变化
        function handleMenuTypeChange() {
            const menuType = document.getElementById('addMenuType').value;
            const parentGroup = document.getElementById('addParentGroup');
            const urlInput = document.getElementById('addMenuUrl');

            if (menuType === 'submenu') {
                parentGroup.style.display = 'block';
                urlInput.placeholder = '输入子菜单链接 (如: /about.php)';
            } else if (menuType === 'dropdown') {
                parentGroup.style.display = 'none';
                urlInput.placeholder = '下拉菜单通常使用 # 作为链接';
                urlInput.value = '#';
            } else {
                parentGroup.style.display = 'none';
                urlInput.placeholder = '输入菜单链接 (如: /services.php)';
                if (urlInput.value === '#') {
                    urlInput.value = '';
                }
            }
        }

        function editNavItem(itemId) {
            selectNavItem(itemId);
        }

        function deleteNavItem(itemId) {
            if (!confirm('确定要删除这个菜单项吗？')) return;

            fetch(`api/navbar.php?id=${itemId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('菜单项删除成功');
                    loadNavbarData();
                } else {
                    alert('删除失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('删除菜单项失败:', error);
                alert('删除失败，请重试');
            });
        }

        function saveMenuItem() {
            if (!selectedMenuItem) {
                alert('请先选择一个菜单项');
                return;
            }

            const formData = {
                id: selectedMenuItem.id,
                name: document.getElementById('menuName').value,
                url: document.getElementById('menuUrl').value,
                icon: document.getElementById('menuIcon').value,
                type: document.getElementById('menuType').value,
                visible: document.getElementById('menuVisible').checked,
                sort_order: parseInt(document.getElementById('menuOrder').value) || 0
            };

            fetch('api/navbar.php', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('菜单项保存成功');
                    loadNavbarData();
                } else {
                    alert('保存失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('保存菜单项失败:', error);
                alert('保存失败，请重试');
            });
        }

        function deleteMenuItem() {
            if (!selectedMenuItem) {
                alert('请先选择一个菜单项');
                return;
            }

            deleteNavItem(selectedMenuItem.id);
        }

        function cancelEdit() {
            selectedMenuItem = null;
            document.querySelectorAll('.nav-tree-item').forEach(item => {
                item.classList.remove('selected');
            });

            const noSelection = document.querySelector('.no-selection');
            const menuForm = document.getElementById('menuForm');

            if (noSelection) noSelection.style.display = 'block';
            if (menuForm) menuForm.style.display = 'none';
        }

        function refreshNavbar() {
            loadNavbarData();
            alert('导航栏数据已刷新');
        }

        function previewNavbar() {
            // 直接打开前台预览页面
            const previewWindow = window.open('preview_navbar.php', '_blank', 'width=1200,height=800');
            if (!previewWindow) {
                alert('请允许弹窗以查看预览效果');
            }
        }

        function showNavbarPreview(html) {
            // 创建预览模态框
            const modal = document.createElement('div');
            modal.className = 'navbar-preview-modal';
            modal.style.display = 'flex';

            modal.innerHTML = `
                <div class="navbar-preview-content">
                    <div class="preview-header">
                        <h3>导航栏预览</h3>
                        <button class="btn btn-secondary" onclick="closePreview()">关闭</button>
                    </div>
                    <div class="preview-body">
                        <div class="preview-navbar">
                            ${html}
                        </div>
                        <div style="padding: 2rem; background: #f8fafc;">
                            <h4>预览说明</h4>
                            <p>这是根据当前导航栏配置生成的预览效果。实际样式可能会根据前台CSS有所不同。</p>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 添加关闭功能
            window.closePreview = function() {
                document.body.removeChild(modal);
                delete window.closePreview;
            };

            // 点击外部关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    window.closePreview();
                }
            });
        }

        function loadNavbarData() {
            fetch('api/navbar.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentNavbarData = data.data;
                    renderNavbarTree();
                } else {
                    console.error('加载导航栏数据失败:', data.error);
                }
            })
            .catch(error => {
                console.error('加载导航栏数据失败:', error);
            });
        }

        // 主页设计器功能
        let currentHomepageData = {};

        // 初始化主页设计器
        function initHomepageDesigner() {
            // 加载现有内容
            loadHomepageContent();

            // 绑定标签切换事件
            document.querySelectorAll('.section-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const section = this.dataset.section;
                    switchSection(section);
                });
            });

            // 绑定表单事件
            bindFormEvents();
        }

        // 切换区域标签
        function switchSection(section) {
            // 更新标签状态
            document.querySelectorAll('.section-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-section="${section}"]`).classList.add('active');

            // 显示对应面板
            document.querySelectorAll('.section-panel').forEach(panel => {
                panel.style.display = 'none';
            });
            document.getElementById(`${section}-panel`).style.display = 'block';

            // 如果切换到O'Reilly区域，加载数据并初始化标签
            if (section === 'oreilly') {
                loadOreillyContent();
                initOreillyTabs();
            }
        }

        // 初始化O'Reilly区域标签切换
        function initOreillyTabs() {
            document.querySelectorAll('.oreilly-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const section = this.dataset.oreillySection;
                    switchOreillySection(section);
                });
            });
        }

        // 切换O'Reilly子区域
        function switchOreillySection(section) {
            // 更新标签状态
            document.querySelectorAll('.oreilly-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-oreilly-section="${section}"]`).classList.add('active');

            // 显示对应内容
            document.querySelectorAll('.oreilly-content').forEach(content => {
                content.classList.remove('active');
                content.style.display = 'none';
            });
            document.getElementById(`oreilly-${section}-content`).classList.add('active');
            document.getElementById(`oreilly-${section}-content`).style.display = 'block';
        }

        // 加载O'Reilly区域内容
        function loadOreillyContent() {
            fetch('api/oreilly-content.php?action=all')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        populateOreillyForms(data.data);
                    } else {
                        console.error('加载O\'Reilly内容失败:', data.error);
                    }
                })
                .catch(error => {
                    console.error('加载O\'Reilly内容出错:', error);
                });
        }

        // 填充O'Reilly表单数据
        function populateOreillyForms(data) {
            // 填充英雄区域
            if (data.hero) {
                document.getElementById('oreillyHeroTitle').value = data.hero.main_title || '';
                document.getElementById('oreillyHeroDescription').value = data.hero.description || '';
                document.getElementById('oreillyHeroPrimaryBtnText').value = data.hero.primary_button_text || '';
                document.getElementById('oreillyHeroPrimaryBtnUrl').value = data.hero.primary_button_url || '';
                document.getElementById('oreillyHeroSecondaryBtnText').value = data.hero.secondary_button_text || '';
                document.getElementById('oreillyHeroSecondaryBtnUrl').value = data.hero.secondary_button_url || '';
            }

            // 填充课程区域
            if (data.courses) {
                if (data.courses.live_courses) {
                    document.getElementById('liveCoursesTitle').value = data.courses.live_courses.title || '';
                    document.getElementById('liveCoursesDescription').value = data.courses.live_courses.description || '';
                    document.getElementById('liveCoursesButtonText').value = data.courses.live_courses.button_text || '';
                    document.getElementById('liveCoursesButtonUrl').value = data.courses.live_courses.button_url || '';
                }
                if (data.courses.ai_answers) {
                    document.getElementById('aiAnswersTitle').value = data.courses.ai_answers.title || '';
                    document.getElementById('aiAnswersDescription').value = data.courses.ai_answers.description || '';
                    document.getElementById('aiAnswersButtonText').value = data.courses.ai_answers.button_text || '';
                    document.getElementById('aiAnswersButtonUrl').value = data.courses.ai_answers.button_url || '';
                }
            }

            // 填充专家展示区域
            if (data.experts) {
                document.getElementById('oreillyExpertsTitle').value = data.experts.main_title || '';
                document.getElementById('oreillyExpertsDescription').value = data.experts.description || '';
            }

            // 填充推荐区域
            if (data.testimonial) {
                document.getElementById('testimonialTitle').value = data.testimonial.title || '';
                document.getElementById('testimonialDescription').value = data.testimonial.description || '';
                document.getElementById('stat1Number').value = data.testimonial.stat1_number || '';
                document.getElementById('stat1Label').value = data.testimonial.stat1_label || '';
                document.getElementById('stat2Number').value = data.testimonial.stat2_number || '';
                document.getElementById('stat2Label').value = data.testimonial.stat2_label || '';
                document.getElementById('stat3Number').value = data.testimonial.stat3_number || '';
                document.getElementById('stat3Label').value = data.testimonial.stat3_label || '';
                document.getElementById('testimonialButtonText').value = data.testimonial.button_text || '';
                document.getElementById('testimonialButtonUrl').value = data.testimonial.button_url || '';
            }

            // 填充行动号召区域
            if (data.cta) {
                document.getElementById('oreillyCtaTitle').value = data.cta.main_title || '';
                document.getElementById('oreillyCtaPrimaryBtnText').value = data.cta.primary_button_text || '';
                document.getElementById('oreillyCtaPrimaryBtnUrl').value = data.cta.primary_button_url || '';
                document.getElementById('oreillyCtaSecondaryBtnText').value = data.cta.secondary_button_text || '';
                document.getElementById('oreillyCtaSecondaryBtnUrl').value = data.cta.secondary_button_url || '';
            }
        }

        // 绑定表单事件
        function bindFormEvents() {
            // 英雄区域表单实时更新
            const heroForm = document.getElementById('heroForm');
            if (heroForm) {
                heroForm.addEventListener('input', function(e) {
                    if (e.target.type !== 'file') {
                        updatePreview();
                    }
                });
            }

            // 文件上传处理
            const heroImageInput = document.getElementById('heroImage');
            if (heroImageInput) {
                heroImageInput.addEventListener('change', handleImageUpload);
            }

            const videoFileInput = document.getElementById('videoFile');
            if (videoFileInput) {
                videoFileInput.addEventListener('change', handleVideoUpload);
            }
        }

        // 加载主页内容
        function loadHomepageContent() {
            fetch('api/homepage-content.php?action=all')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentHomepageData = data.data;
                    populateForm();
                    loadExperts();
                    loadSections();
                } else {
                    console.error('加载主页内容失败:', data.error);
                }
            })
            .catch(error => {
                console.error('加载主页内容失败:', error);
            });
        }

        // 填充表单数据
        function populateForm() {
            const hero = currentHomepageData.hero;
            if (hero) {
                document.getElementById('heroTitle').value = hero.title || '';
                document.getElementById('heroSubtitle').value = hero.subtitle || '';
                document.getElementById('heroDescription').value = hero.description || '';
                document.getElementById('primaryButtonText').value = hero.primary_button_text || '';
                document.getElementById('primaryButtonUrl').value = hero.primary_button_url || '';
                document.getElementById('secondaryButtonText').value = hero.secondary_button_text || '';
                document.getElementById('secondaryButtonUrl').value = hero.secondary_button_url || '';

                if (hero.hero_image) {
                    showCurrentImage(hero.hero_image);
                }
            }

            const video = currentHomepageData.video;
            if (video) {
                document.getElementById('videoTitle').value = video.title || '';
                document.getElementById('videoDescription').value = video.description || '';
                document.getElementById('speakerName').value = video.speaker_name || '';
                document.getElementById('speakerRole').value = video.speaker_role || '';
                document.getElementById('companyBadge').value = video.company_badge || '';
            }
        }

        // 显示当前图片
        function showCurrentImage(imagePath) {
            const currentImageDiv = document.getElementById('currentHeroImage');
            const img = currentImageDiv.querySelector('img');
            img.src = imagePath;
            currentImageDiv.style.display = 'block';
        }

        // 处理图片上传
        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const formData = new FormData();
                formData.append('image', file);
                formData.append('type', 'hero_image');

                // 显示上传进度
                const uploadStatus = document.createElement('div');
                uploadStatus.textContent = '正在上传...';
                uploadStatus.style.color = '#3b82f6';
                event.target.parentElement.appendChild(uploadStatus);

                // 上传到服务器
                fetch('api/upload.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    uploadStatus.remove();
                    if (data.success) {
                        showCurrentImage(data.path);
                        // 更新当前数据
                        if (currentHomepageData.hero) {
                            currentHomepageData.hero.hero_image = data.path;
                        }
                        alert('图片上传成功！');
                    } else {
                        alert('上传失败: ' + data.error);
                    }
                })
                .catch(error => {
                    uploadStatus.remove();
                    console.error('上传失败:', error);
                    alert('上传失败，请重试');
                });
            }
        }

        // 处理视频上传
        function handleVideoUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const formData = new FormData();
                formData.append('video', file);
                formData.append('type', 'video');

                // 显示上传进度
                const uploadStatus = document.createElement('div');
                uploadStatus.textContent = '正在上传视频，请稍候...';
                uploadStatus.style.color = '#3b82f6';
                event.target.parentElement.appendChild(uploadStatus);

                // 上传到服务器
                fetch('api/upload.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    uploadStatus.remove();
                    if (data.success) {
                        // 显示视频预览
                        const currentVideoDiv = document.getElementById('currentVideo');
                        const video = currentVideoDiv.querySelector('video source');
                        video.src = data.path;
                        currentVideoDiv.style.display = 'block';
                        alert('视频上传成功！');
                    } else {
                        alert('上传失败: ' + data.error);
                    }
                })
                .catch(error => {
                    uploadStatus.remove();
                    console.error('上传失败:', error);
                    alert('上传失败，请重试');
                });
            }
        }

        // 移除英雄图片
        function removeHeroImage() {
            document.getElementById('currentHeroImage').style.display = 'none';
            document.getElementById('heroImage').value = '';
        }

        // 移除视频
        function removeVideo() {
            document.getElementById('currentVideo').style.display = 'none';
            document.getElementById('videoFile').value = '';
        }

        // 加载专家列表
        function loadExperts() {
            const experts = currentHomepageData.experts || [];
            const expertsList = document.getElementById('expertsList');
            expertsList.innerHTML = '';

            experts.forEach(expert => {
                const expertItem = createExpertItem(expert);
                expertsList.appendChild(expertItem);
            });
        }

        // 创建专家项目
        function createExpertItem(expert) {
            const div = document.createElement('div');
            div.className = 'expert-item';
            div.innerHTML = `
                <button class="remove-expert" onclick="removeExpert(${expert.id})">删除</button>
                <div class="form-row">
                    <div class="form-group">
                        <label>姓名</label>
                        <input type="text" value="${expert.name}" onchange="updateExpert(${expert.id}, 'name', this.value)">
                    </div>
                    <div class="form-group">
                        <label>职位</label>
                        <input type="text" value="${expert.title}" onchange="updateExpert(${expert.id}, 'title', this.value)">
                    </div>
                </div>
                <div class="form-group">
                    <label>描述</label>
                    <textarea onchange="updateExpert(${expert.id}, 'description', this.value)">${expert.description || ''}</textarea>
                </div>
                <div class="form-group">
                    <label>头像</label>
                    <input type="file" accept="image/*" onchange="updateExpertImage(${expert.id}, this)">
                    <div class="current-image" style="margin-top: 8px;">
                        <img src="${expert.image}" alt="${expert.name}" style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover;">
                    </div>
                </div>
            `;
            return div;
        }

        // 添加新专家
        function addNewExpert() {
            const newExpert = {
                name: '新专家',
                title: '职位',
                description: '',
                image: 'image/default-avatar.svg',
                sort_order: 0
            };

            fetch('api/homepage-content.php?action=expert', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(newExpert)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadHomepageContent(); // 重新加载数据
                } else {
                    alert('添加专家失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('添加专家失败:', error);
                alert('添加专家失败，请重试');
            });
        }

        // 更新专家信息
        function updateExpert(id, field, value) {
            const expert = currentHomepageData.experts.find(e => e.id === id);
            if (expert) {
                expert[field] = value;

                fetch('api/homepage-content.php?action=expert', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(expert)
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        alert('更新专家信息失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('更新专家信息失败:', error);
                });
            }
        }

        // 更新专家头像
        function updateExpertImage(id, input) {
            const file = input.files[0];
            if (file) {
                const formData = new FormData();
                formData.append('image', file);
                formData.append('type', 'expert_image');
                formData.append('expert_id', id);

                // 显示上传进度
                const uploadStatus = document.createElement('div');
                uploadStatus.textContent = '正在上传...';
                uploadStatus.style.color = '#3b82f6';
                input.parentElement.appendChild(uploadStatus);

                // 上传到服务器
                fetch('api/upload.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    uploadStatus.remove();
                    if (data.success) {
                        // 更新专家数据
                        updateExpert(id, 'image', data.path);
                        const img = input.parentElement.querySelector('img');
                        img.src = data.path;
                        alert('头像上传成功！');
                    } else {
                        alert('上传失败: ' + data.error);
                    }
                })
                .catch(error => {
                    uploadStatus.remove();
                    console.error('上传失败:', error);
                    alert('上传失败，请重试');
                });
            }
        }

        // 删除专家
        function removeExpert(id) {
            if (!confirm('确定要删除这个专家吗？')) return;

            fetch(`api/homepage-content.php?action=expert&id=${id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadHomepageContent(); // 重新加载数据
                } else {
                    alert('删除专家失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('删除专家失败:', error);
                alert('删除专家失败，请重试');
            });
        }

        // 加载区域设置
        function loadSections() {
            const sections = currentHomepageData.sections || [];
            const sectionsList = document.getElementById('sectionsList');
            sectionsList.innerHTML = '';

            sections.forEach(section => {
                const sectionItem = createSectionItem(section);
                sectionsList.appendChild(sectionItem);
            });
        }

        // 创建区域项目
        function createSectionItem(section) {
            const div = document.createElement('div');
            div.className = 'section-item';
            div.innerHTML = `
                <div class="section-info">
                    <h4>${section.section_title}</h4>
                    <p>${section.section_description}</p>
                </div>
                <div class="section-toggle">
                    <span>显示</span>
                    <div class="toggle-switch ${section.is_visible ? 'active' : ''}" onclick="toggleSection(${section.id}, this)"></div>
                </div>
            `;
            return div;
        }

        // 切换区域显示状态
        function toggleSection(id, toggleElement) {
            const isVisible = !toggleElement.classList.contains('active');

            fetch('api/homepage-content.php?action=section', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: id, is_visible: isVisible })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    toggleElement.classList.toggle('active');
                } else {
                    alert('更新区域设置失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('更新区域设置失败:', error);
                alert('更新区域设置失败，请重试');
            });
        }



        // 预览主页
        function previewHomepage() {
            const previewWindow = window.open('index.php', '_blank', 'width=1200,height=800');
            if (!previewWindow) {
                alert('请允许弹窗以查看预览效果');
            }
        }

        // 更新预览
        function updatePreview() {
            const iframe = document.querySelector('#previewFrame iframe');
            if (iframe) {
                iframe.src = iframe.src; // 刷新iframe
            }
        }

        // 在页面加载完成后初始化主页设计器
        document.addEventListener('DOMContentLoaded', function() {
            // 原有的初始化代码...

            // 检查是否在页面管理页面（包含主页设计器）
            const pageManagement = document.getElementById('page-management');
            if (pageManagement) {
                // 延迟初始化，确保页面完全加载
                setTimeout(() => {
                    if (pageManagement.style.display !== 'none') {
                        initHomepageDesigner();
                    }
                }, 100);
            }
        });

        // 修改switchPage函数以支持主页设计器
        const originalSwitchPage = window.switchPage;
        window.switchPage = function(pageId) {
            originalSwitchPage(pageId);

            // 如果切换到页面管理（包含主页设计器），初始化功能
            if (pageId === 'page-management') {
                setTimeout(() => {
                    initHomepageDesigner();
                }, 100);
            }
        };

        // 保存主页内容
        function saveHomepageContent() {
            const currentSection = document.querySelector('.section-tab.active').dataset.section;

            if (currentSection === 'hero') {
                saveHeroContent();
            } else if (currentSection === 'video') {
                saveVideoContent();
            } else if (currentSection === 'experts') {
                // 专家数据会在编辑时自动保存
                alert('专家数据已自动保存！');
            } else if (currentSection === 'sections') {
                saveSectionsContent();
            }
        }

        // 保存英雄区域内容
        function saveHeroContent() {
            const heroData = {
                title: document.getElementById('heroTitle').value,
                subtitle: document.getElementById('heroSubtitle').value,
                description: document.getElementById('heroDescription').value,
                primary_button_text: document.getElementById('primaryButtonText').value,
                primary_button_url: document.getElementById('primaryButtonUrl').value,
                secondary_button_text: document.getElementById('secondaryButtonText').value,
                secondary_button_url: document.getElementById('secondaryButtonUrl').value
            };

            fetch('api/homepage-content.php?action=hero', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(heroData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('英雄区域内容保存成功！');
                    // 更新当前数据
                    if (currentHomepageData.hero) {
                        Object.assign(currentHomepageData.hero, heroData);
                    }
                } else {
                    alert('保存失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                alert('保存失败，请重试');
            });
        }

        // 保存视频内容
        function saveVideoContent() {
            const videoData = {
                title: document.getElementById('videoTitle').value,
                description: document.getElementById('videoDescription').value,
                speaker_name: document.getElementById('videoSpeakerName').value,
                speaker_role: document.getElementById('videoSpeakerRole').value
            };

            fetch('api/homepage-content.php?action=video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(videoData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('视频内容保存成功！');
                    // 更新当前数据
                    if (currentHomepageData.video) {
                        Object.assign(currentHomepageData.video, videoData);
                    }
                } else {
                    alert('保存失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                alert('保存失败，请重试');
            });
        }

        // 保存O'Reilly英雄区域内容
        function saveOreillyHero() {
            const heroData = {
                main_title: document.getElementById('oreillyHeroTitle').value,
                description: document.getElementById('oreillyHeroDescription').value,
                primary_button_text: document.getElementById('oreillyHeroPrimaryBtnText').value,
                primary_button_url: document.getElementById('oreillyHeroPrimaryBtnUrl').value,
                secondary_button_text: document.getElementById('oreillyHeroSecondaryBtnText').value,
                secondary_button_url: document.getElementById('oreillyHeroSecondaryBtnUrl').value
            };

            fetch('api/oreilly-content.php?action=hero', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(heroData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('技能建设区域保存成功！');
                } else {
                    alert('保存失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                alert('保存失败，请重试');
            });
        }

        // 保存O'Reilly专家课程区域内容
        function saveOreillyCourses() {
            const coursesData = {
                live_courses: {
                    title: document.getElementById('liveCoursesTitle').value,
                    description: document.getElementById('liveCoursesDescription').value,
                    button_text: document.getElementById('liveCoursesButtonText').value,
                    button_url: document.getElementById('liveCoursesButtonUrl').value
                },
                ai_answers: {
                    title: document.getElementById('aiAnswersTitle').value,
                    description: document.getElementById('aiAnswersDescription').value,
                    button_text: document.getElementById('aiAnswersButtonText').value,
                    button_url: document.getElementById('aiAnswersButtonUrl').value
                }
            };

            fetch('api/oreilly-content.php?action=courses', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(coursesData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('专家课程区域保存成功！');
                } else {
                    alert('保存失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                alert('保存失败，请重试');
            });
        }

        // 保存O'Reilly专家展示区域内容
        function saveOreillyExperts() {
            const expertsData = {
                main_title: document.getElementById('oreillyExpertsTitle').value,
                description: document.getElementById('oreillyExpertsDescription').value
            };

            fetch('api/oreilly-content.php?action=experts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(expertsData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('知识分享区域保存成功！');
                } else {
                    alert('保存失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                alert('保存失败，请重试');
            });
        }

        // 保存O'Reilly推荐区域内容
        function saveOreillyTestimonial() {
            const testimonialData = {
                title: document.getElementById('testimonialTitle').value,
                description: document.getElementById('testimonialDescription').value,
                stat1_number: document.getElementById('stat1Number').value,
                stat1_label: document.getElementById('stat1Label').value,
                stat2_number: document.getElementById('stat2Number').value,
                stat2_label: document.getElementById('stat2Label').value,
                stat3_number: document.getElementById('stat3Number').value,
                stat3_label: document.getElementById('stat3Label').value,
                button_text: document.getElementById('testimonialButtonText').value,
                button_url: document.getElementById('testimonialButtonUrl').value
            };

            fetch('api/oreilly-content.php?action=testimonial', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testimonialData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('推荐区域保存成功！');
                } else {
                    alert('保存失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                alert('保存失败，请重试');
            });
        }

        // 保存O'Reilly行动号召区域内容
        function saveOreillyCta() {
            const ctaData = {
                main_title: document.getElementById('oreillyCtaTitle').value,
                primary_button_text: document.getElementById('oreillyCtaPrimaryBtnText').value,
                primary_button_url: document.getElementById('oreillyCtaPrimaryBtnUrl').value,
                secondary_button_text: document.getElementById('oreillyCtaSecondaryBtnText').value,
                secondary_button_url: document.getElementById('oreillyCtaSecondaryBtnUrl').value
            };

            fetch('api/oreilly-content.php?action=cta', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(ctaData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('帮助团队区域保存成功！');
                } else {
                    alert('保存失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                alert('保存失败，请重试');
            });
        }

        // 在新窗口中打开主页预览
        function openHomepagePreview() {
            window.open('index.php', '_blank');
        }

        // 应用管理卡片点击函数
        function openCommunityManagement() {
            window.open('community-management.php', '_blank');
        }

        function openUserManagement() {
            switchPage('user-management');
        }

        function openContentManagement() {
            // 这里可以添加内容管理页面的逻辑
            alert('内容管理功能正在开发中...');
        }

        function openSystemSettings() {
            switchPage('system-settings');
        }

        function openDataAnalytics() {
            // 这里可以添加数据分析页面的逻辑
            alert('数据分析功能正在开发中...');
        }

        function openHealthManagement() {
            window.open('health-management.php', '_blank');
        }

        function openStudentAffairs() {
            window.open('student-affairs.php', '_blank');
        }

        function openBackupManagement() {
            // 这里可以添加备份管理页面的逻辑
            alert('备份管理功能正在开发中...');
        }
    </script>
</body>
</html>
